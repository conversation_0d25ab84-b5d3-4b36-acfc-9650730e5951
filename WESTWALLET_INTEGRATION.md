# WestWallet Integration

This document describes the WestWallet cryptocurrency payment integration for the Phantom deposit system.

## Overview

The deposit system now supports cryptocurrency payments through WestWallet API, allowing users to pay with Bitcoin (BTC), Ethereum (ETH), Tether (USDT), and Litecoin (LTC).

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# WestWallet API Configuration
WESTWALLET_API_KEY=your_api_key_here
WESTWALLET_SECRET_KEY=your_secret_key_here
WESTWALLET_BASE_URL=https://api.westwallet.io
```

### Getting WestWallet Credentials

1. Visit [WestWallet.io](https://westwallet.io)
2. Create an account and verify your identity
3. Go to [Settings](https://westwallet.io/settings) to get your API credentials
4. Copy your API Key and Secret Key to the environment variables

## Database Schema Updates

The `deposits` table has been extended with the following fields:

```sql
-- WestWallet integration fields
westwallet_invoice_id TEXT,
westwallet_invoice_data JSONB,
payment_addresses JSONB,
payment_status TEXT DEFAULT 'pending',
payment_received_at TIMESTAMPTZ,
payment_transaction_id TEXT,
```

## Architecture

### Components

1. **WestWallet Configuration** (`src/lib/server/westwallet-config.js`)
   - Manages API credentials and configuration
   - Validates environment variables

2. **WestWallet Service** (`src/lib/server/westwallet-service.js`)
   - Encapsulates WestWallet API interactions
   - Handles invoice creation, transaction queries, and balance checks
   - Provides error handling and logging

3. **Deposit Page Server Action** (`src/routes/customer/[slug]/deposit/+page.server.js`)
   - Creates WestWallet invoices for deposits
   - Falls back to placeholder payment URL if WestWallet fails
   - Stores invoice data in the database

4. **Webhook Handler** (`src/routes/api/webhooks/westwallet/+server.js`)
   - Receives payment notifications from WestWallet
   - Verifies webhook signatures for security
   - Updates deposit status and team balance
   - Creates transaction records

5. **Payment Success Page** (`src/routes/customer/[slug]/deposit/success/`)
   - Shows payment status and details
   - Displays transaction information

### Payment Flow

1. **User initiates deposit**
   - User enters amount on deposit page
   - System creates WestWallet invoice
   - Invoice data stored in database

2. **Payment options displayed**
   - User sees cryptocurrency addresses for BTC, ETH, USDT, LTC
   - Each currency shows the exact amount to send
   - User can copy addresses or open payment page

3. **User makes payment**
   - User sends cryptocurrency to provided address
   - WestWallet monitors the blockchain for payments

4. **Payment confirmation**
   - WestWallet sends webhook notification
   - System verifies webhook signature
   - Deposit status updated to 'completed'
   - Team balance increased
   - Transaction record created

## API Endpoints

### Webhook Endpoint

**POST** `/api/webhooks/westwallet`

Receives payment notifications from WestWallet.

**Headers:**
- `X-ACCESS-SIGN`: HMAC-SHA256 signature
- `X-ACCESS-TIMESTAMP`: Unix timestamp

**Security:**
- Webhook signatures are verified using HMAC-SHA256
- Only requests from WestWallet IPs are accepted (***********)

## Testing

### Test WestWallet Integration

Run the test script to verify your WestWallet configuration:

```bash
node src/lib/server/test-westwallet.js
```

This will test:
- API credentials
- Wallet balance retrieval
- Invoice creation
- Error handling

### Manual Testing

1. **Test deposit creation:**
   - Go to `/customer/{device_ip}/deposit`
   - Enter an amount and submit
   - Verify cryptocurrency addresses are displayed

2. **Test webhook (development):**
   - Use ngrok or similar to expose your local server
   - Update WestWallet IPN URL to your public endpoint
   - Make a small test payment

## Error Handling

### Fallback Behavior

If WestWallet API is unavailable:
- System falls back to placeholder payment URL
- Deposit record is still created
- User can still complete payment through alternative means

### Common Issues

1. **Invalid API credentials**
   - Check environment variables
   - Verify credentials in WestWallet dashboard

2. **Webhook signature verification fails**
   - Ensure secret key is correct
   - Check that webhook is coming from WestWallet IP

3. **Invoice creation fails**
   - Check WestWallet account balance
   - Verify API permissions
   - Check rate limits

## Security Considerations

1. **Webhook Verification**
   - All webhooks are signature-verified
   - Timestamps prevent replay attacks

2. **Environment Variables**
   - Keep API credentials secure
   - Use different credentials for development/production

3. **Database Security**
   - Sensitive payment data is stored in JSONB fields
   - Access is restricted through Supabase RLS

## Monitoring

### Logs

The system logs important events:
- Invoice creation success/failure
- Webhook receipt and processing
- Payment confirmations
- Error conditions

### Metrics to Monitor

- Invoice creation success rate
- Payment completion rate
- Webhook processing time
- Failed payment notifications

## Production Deployment

1. **Update environment variables**
   - Set production WestWallet credentials
   - Update base URL for webhooks

2. **Configure webhook URL**
   - Set IPN URL in WestWallet dashboard
   - Ensure webhook endpoint is accessible

3. **Test payment flow**
   - Make test deposits
   - Verify webhook processing
   - Check balance updates

## Support

For WestWallet API issues:
- Documentation: https://westwallet.io/api_docs
- Support: Contact WestWallet support team

For integration issues:
- Check logs for error messages
- Verify environment configuration
- Test with the provided test script
