// SvelteKit POST endpoint to receive FCM tokens from Android app
import { json } from '@sveltejs/kit';
import { promises as fs } from 'fs';

const WEBVIEW_AUTH_TOKEN = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {


    const pass = request.headers.get('pass');

    if (pass !== WEBVIEW_AUTH_TOKEN) {
        console.warn('[POST /api/notifications/token] Unauthorized access attempt');
        return new Response('Unauthorized', { status: 401 });
    }


    let token;
    try {

        const body = await request.json();
        token = body.token;
        const team_id = body.team_id;
        console.log('[POST /api/notifications/token] Received token and team_id from Android app:', token, team_id);

        if (!token) {
            console.error('[POST /api/notifications/token] Missing token in request body');
            throw new Error('Missing token');
        }
    } catch (e) {
        console.error('[POST /api/notifications/token] Invalid request body:', e);
        return new Response('Invalid request', { status: 400 });
    }

    // Store token in tokens.txt (append mode)
    const tokensFile = '../tokens.txt';
    try {

        await fs.appendFile(tokensFile, token + '\n');

    } catch (err) {
        console.error('[POST /api/notifications/token] Failed to save token:', err);
        return new Response('Failed to save token', { status: 500 });
    }



    return json({ success: true });
}
