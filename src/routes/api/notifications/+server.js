// @ts-check

import { json } from '@sveltejs/kit';
import { sendNotification, getRegistrationToken } from '$lib/server/notifications';

/** @type {import('./$types').RequestHandler} */
export async function GET({ request }) {
  const authToken = request.headers.get('X-Phantom-Auth');
  if (!authToken) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
  // FCM is push-based; no notifications to "get" unless you store them elsewhere
  return json({ notifications: [] });
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  const authToken = request.headers.get('X-Phantom-Auth');
  if (!authToken) {
    return json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const { title, message } = await request.json();
    try {
      const result = await sendNotification({ title, message, authToken });
      return json(result);
    } catch (err) {
      if (err.message === 'Missing required fields') {
        return json({ error: err.message }, { status: 400 });
      }
      if (err.message === 'No registration token for user') {
        return json({ error: err.message }, { status: 404 });
      }
      return json({ error: err.message }, { status: 500 });
    }
  } catch (error) {
    return json({ error: 'Invalid request body' }, { status: 400 });
  }
}