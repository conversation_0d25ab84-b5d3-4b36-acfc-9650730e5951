import { json } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase.js';
import crypto from 'crypto';
import { westwalletConfig } from '$lib/server/westwallet-config.js';

/**
 * Verify WestWallet webhook signature
 * @param {string} body - Raw request body
 * @param {string} signature - Signature from X-ACCESS-SIGN header
 * @param {string} timestamp - Timestamp from X-ACCESS-TIMESTAMP header
 * @returns {boolean} True if signature is valid
 */
function verifyWebhookSignature(body, signature, timestamp) {
  try {
    const message = `${timestamp}${body}`;
    const expectedSignature = crypto
      .createHmac('sha256', westwalletConfig.secretKey)
      .update(message)
      .digest('hex');
    
    return signature === expectedSignature;
  } catch (error) {
    console.error('[WestWallet Webhook] Signature verification error:', error);
    return false;
  }
}

/**
 * Process payment confirmation
 * @param {Object} paymentData - Payment data from WestWallet
 */
async function processPaymentConfirmation(paymentData) {
  const { invoice_id, transaction_id, amount, currency, status } = paymentData;
  
  console.log('[WestWallet Webhook] Processing payment confirmation:', {
    invoice_id,
    transaction_id,
    amount,
    currency,
    status
  });

  try {
    // Find the deposit record by WestWallet invoice ID
    const { data: depositData, error: findError } = await supabase
      .from('deposits')
      .select('*')
      .eq('westwallet_invoice_id', invoice_id)
      .single();

    if (findError || !depositData) {
      console.error('[WestWallet Webhook] Deposit not found for invoice:', invoice_id);
      return { success: false, error: 'Deposit not found' };
    }

    // Update deposit status
    const updateData = {
      payment_status: status === 'confirmed' ? 'completed' : 'failed',
      payment_received_at: new Date().toISOString(),
      payment_transaction_id: transaction_id
    };

    const { error: updateError } = await supabase
      .from('deposits')
      .update(updateData)
      .eq('internal_id', depositData.internal_id);

    if (updateError) {
      console.error('[WestWallet Webhook] Failed to update deposit:', updateError);
      return { success: false, error: 'Failed to update deposit' };
    }

    // If payment is confirmed, update team balance
    if (status === 'confirmed') {
      // Get current team data
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('balance')
        .eq('internal_id', depositData.team_internal_id)
        .single();

      if (teamError) {
        console.error('[WestWallet Webhook] Failed to get team data:', teamError);
        return { success: false, error: 'Failed to get team data' };
      }

      // Calculate new balance
      const newBalance = parseFloat(teamData.balance) + parseFloat(depositData.amount);

      // Update team balance
      const { error: balanceError } = await supabase
        .from('teams')
        .update({ balance: newBalance })
        .eq('internal_id', depositData.team_internal_id);

      if (balanceError) {
        console.error('[WestWallet Webhook] Failed to update team balance:', balanceError);
        return { success: false, error: 'Failed to update team balance' };
      }

      // Create transaction record
      const transactionId = BigInt(Date.now()) * 1000n + BigInt(Math.floor(Math.random() * 1000));
      
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          internal_id: transactionId.toString(),
          created_at: new Date().toISOString(),
          team_internal_id: depositData.team_internal_id,
          team_id: depositData.team_id,
          amount: depositData.amount,
          description: `Deposit via ${currency} - ${transaction_id}`,
          balance_before: teamData.balance,
          balance_after: newBalance
        });

      if (transactionError) {
        console.error('[WestWallet Webhook] Failed to create transaction record:', transactionError);
        // Don't return error here as the main payment processing succeeded
      }

      console.log('[WestWallet Webhook] Payment processed successfully:', {
        depositId: depositData.internal_id,
        teamId: depositData.team_id,
        amount: depositData.amount,
        newBalance
      });
    }

    return { success: true };
  } catch (error) {
    console.error('[WestWallet Webhook] Error processing payment:', error);
    return { success: false, error: error.message };
  }
}

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    // Get headers
    const signature = request.headers.get('x-access-sign');
    const timestamp = request.headers.get('x-access-timestamp');
    
    if (!signature || !timestamp) {
      console.error('[WestWallet Webhook] Missing required headers');
      return json({ error: 'Missing required headers' }, { status: 400 });
    }

    // Get raw body
    const body = await request.text();
    
    // Verify signature
    if (!verifyWebhookSignature(body, signature, timestamp)) {
      console.error('[WestWallet Webhook] Invalid signature');
      return json({ error: 'Invalid signature' }, { status: 401 });
    }

    // Parse JSON
    let paymentData;
    try {
      paymentData = JSON.parse(body);
    } catch (parseError) {
      console.error('[WestWallet Webhook] Invalid JSON:', parseError);
      return json({ error: 'Invalid JSON' }, { status: 400 });
    }

    console.log('[WestWallet Webhook] Received payment notification:', paymentData);

    // Process the payment
    const result = await processPaymentConfirmation(paymentData);
    
    if (result.success) {
      return json({ status: 'ok' });
    } else {
      return json({ error: result.error }, { status: 500 });
    }

  } catch (error) {
    console.error('[WestWallet Webhook] Unexpected error:', error);
    return json({ error: 'Internal server error' }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  // Return a simple response for health checks
  return json({ status: 'WestWallet webhook endpoint is active' });
}
