<script>
    import { enhance } from '$app/forms';

    let loading = false;
    let error = null;
    let success = false;
    let title = '';
    let message = '';

    // Reset error/success on input
    function onInput() {
        error = null;
        success = false;
    }

    function handleSubmit() {
        loading = true;
        error = null;
        success = false;
    }

    function handleResult(result) {
        loading = false;
        result = result?.data;
        if (result?.success) {
            success = true;
            title = '';
            message = '';
        } else if (result?.error) {
            error = result.error;
        } else if (result?.type === 'error' && result?.status) {
            error = `Server error (${result.status})`;
        } else {
            error = 'Unknown error occurred.';
        }
    }
</script>

<div class="container">
    <h1>Notification Testing</h1>

    <div class="form-section">
        <h2>Send Test Notification</h2>
        <form 
            method="POST" 
            action="?/sendNotification"
            use:enhance={(form) => {
                handleSubmit();
                return async ({ result }) => handleResult(result);
            }}
        >
            <div class="form-group">
                <label for="title">Title:</label>
                <input 
                    type="text" 
                    id="title" 
                    name="title"
                    bind:value={title} 
                    required
                    placeholder="Notification title"
                    on:input={onInput}
                />
            </div>

            <div class="form-group">
                <label for="message">Message:</label>
                <textarea 
                    id="message" 
                    name="message"
                    bind:value={message} 
                    required
                    placeholder="Notification message"
                    on:input={onInput}
                ></textarea>
            </div>

            <button type="submit" disabled={loading}>
                {loading ? 'Sending...' : 'Send Notification'}
            </button>
        </form>

        {#if error}
            <div class="error">{error}</div>
        {/if}

        {#if success}
            <div class="success">Notification sent successfully!</div>
        {/if}
    </div>
</div>

<style>
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }

    input, textarea {
        width: 100%;
        padding: 0.5rem;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        border-radius: 4px;
    }

    textarea {
        min-height: 100px;
    }

    button {
        background-color: #4CAF50;
        color: white;
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    button:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }

    .error {
        color: #ff0000;
        margin-top: 1rem;
    }

    .success {
        color: #4CAF50;
        margin-top: 1rem;
    }

    .notifications-section {
        margin-top: 2rem;
    }

    .notifications-list {
        margin-top: 1rem;
    }

    .notification {
        background-color: #f5f5f5;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 4px;
    }

    .notification h3 {
        margin: 0 0 0.5rem 0;
    }

    .notification p {
        margin: 0 0 0.5rem 0;
    }

    .notification small {
        color: #666;
    }
</style> 