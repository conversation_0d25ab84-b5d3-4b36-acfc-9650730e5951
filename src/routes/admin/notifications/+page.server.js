import { fail, redirect } from '@sveltejs/kit';

/** @type {import('./$types').Actions} */
export const actions = {
    sendNotification: async ({ request, locals, fetch, cookies }) => {
        const form = await request.formData();
        const title = form.get('title');
        const message = form.get('message');
        console.log(title, message);

        const authToken = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';
        if (!authToken) {
            console.log('No auth token found');
            return fail(401, { error: 'Unauthorized: No auth token found' });
        }

        try {
            const result = await import('$lib/server/notifications').then(mod => mod.sendNotification({ title, message }));
            console.log('good', result);
            return { success: true, response: result.response };
        } catch (err) {
            console.error(err);
            return fail(500, { error: err.message || 'Failed to send notification' });
        }
    }
};
