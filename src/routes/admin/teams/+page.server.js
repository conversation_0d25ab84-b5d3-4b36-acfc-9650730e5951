import { supabase } from '$lib/server/supabase';
import { v4 as uuidv4 } from 'uuid';

export async function load() {
  try {
    const { data, error } = await supabase.from('teams').select('balance'); // Select balance to calculate total
    const teams = data || [];
    
    // Calculate totalteams and totalBalance
    const totalTeams = teams.length;
    const totalBalance = teams.reduce((sum, team) => sum + (team.balance || 0), 0);

    // Re-fetch all team data for display
    const { data: allteamsData, error: allteamsError } = await supabase.from('teams').select('*');

    return {
      teams: allteamsData || [],
      error: error || allteamsError, // Return any error encountered
      totalTeams,
      totalBalance
    };
  } catch (e) {
    // @ts-ignore // Assume e is an Error
    return { teams: [], error: e.message, totalTeams: 0, totalBalance: 0 };
  }
}

export const actions = {
    create: async ({ request }) => {
        const formData = await request.formData();
        const id = formData.get('id');
        const balanceEntry = formData.get('balance');
        const balance = (typeof balanceEntry === 'string') ? parseFloat(balanceEntry) : NaN;
        const next_charge_at = formData.get('next_charge_at');
        const role = formData.get('role');
        const owner_internal_id = formData.get('owner_internal_id');
        const owner_id = formData.get('owner_id');

        // Validate required fields
        if (!id || typeof balance !== 'number' || isNaN(balance)) {
            return { success: false, error: 'Missing or invalid required fields: id, balance' };
        }

        const now = new Date().toISOString();
        const team = {
            internal_id: uuidv4(), // Always generate a new internal ID on create
            id: String(id), // Ensure id is a string
            created_at: now,
            next_charge_at: (next_charge_at && typeof next_charge_at === 'string') ? new Date(next_charge_at).toISOString() : null, // Ensure next_charge_at is a string
            balance: balance,
            role: role ? String(role) : null,
            owner_internal_id: owner_internal_id ? String(owner_internal_id) : null,
            owner_id: owner_id ? String(owner_id) : null
        };

        const { data, error } = await supabase.from('teams').insert([team]).select().single();

        if (error) {
            return { success: false, error: error.message };
        }

        return { success: true, data };
    },
    delete: async ({ request }) => {
        const formData = await request.formData();
        const id = formData.get('id');

        if (!id || typeof id !== 'string') {
            return { success: false, error: 'Missing or invalid team ID' };
        }

        const { error } = await supabase.from('teams').delete().eq('id', id);

        if (error) {
            return { success: false, error: error.message };
        }

        return { success: true };
    }
}; 