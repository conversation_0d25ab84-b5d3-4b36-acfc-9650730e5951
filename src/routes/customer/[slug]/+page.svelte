<script>
  import "./customer.css";
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";

  // Get the data from the server load function
  export let data;

  const { team, deviceIp } = data;

  let bgLoaded = false;

  onMount(() => {
    document.body.addEventListener("touchmove", (e) => {
      if (e.target.tagName !== "INPUT") e.preventDefault();
    }, { passive: false });
  });

  function calculateChargeProgress(nextChargeDate) {
    const now = new Date();
    const chargeDate = new Date(nextChargeDate);
    if (isNaN(chargeDate.getTime()) || chargeDate <= now) return 100;
    const totalDays = 30;
    const timeDiff = chargeDate - now;
    const daysUntilCharge = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
    return Math.round(Math.max(0, 100 - (daysUntilCharge / totalDays) * 100));
  }

  function getTimeUntilNextCharge(nextChargeDate) {
    const now = new Date();
    const chargeDate = new Date(nextChargeDate);
    if (isNaN(chargeDate.getTime()) || chargeDate <= now) return "Due now";
    const diffMs = chargeDate - now;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const parts = [];
    if (diffDays > 0) parts.push(`${diffDays} day${diffDays !== 1 ? "s" : ""}`);
    if (diffHours > 0 || diffDays === 0) parts.push(`${diffHours} hour${diffHours !== 1 ? "s" : ""}`);
    return `in ${parts.join(" ")}`;
  }
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <div class="logo-title">
          <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
          <h1>Phantom</h1>
        </div>
        {#if deviceIp}
          <div class="ip-display">
            <span class="info-icon">🌐</span>
            <span class="ip-address">{deviceIp}</span>
          </div>
        {/if}
      </div>
    </div>
  </div>

  <div class="balance-card">
    <div class="balance-label">Available Balance</div>
    <div class="balance-amount">${(team?.balance || 0).toFixed(2)}</div>
    <button class="action-button" on:click={() => goto(`/customer/${deviceIp}/deposit`, { replaceState: true, invalidateAll: false })}>Add Funds</button>
  </div>

  {#if team?.next_charge_at}
    <div class="next-charge-card">
      <div class="next-charge-amount-row">
        <span class="next-charge-label">Next Scheduled Charge</span>
        <span class="next-charge-amount">${team.next_charge_amount?.toFixed(2) || "0.00"}</span>
      </div>
      <div class="next-charge-details">
        <div class="next-charge-date">
          <span class="icon">📅</span>
          {new Date(team.next_charge_at).toLocaleDateString(undefined, { month: "short", day: "numeric", hour: "2-digit", minute: "2-digit", hour12: true })}
        </div>
        <div class="next-charge-time-remaining">
          <span class="icon">⏱️</span>
          {getTimeUntilNextCharge(team.next_charge_at)}
        </div>
      </div>
      <div class="progress-container">
        <div class="progress-bar" style="width: {calculateChargeProgress(team.next_charge_at)}%"></div>
      </div>
    </div>
  {/if}

  <div class="billing-card">
    <div class="section-title">History</div>
    <div class="transactions-container">
      {#each [{ date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), description: "VPN Service - May 2025", amount: -9.99, balance: 45.21 }, { date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), description: "Top up", amount: 50.0, balance: 55.2 }, { date: new Date(Date.now() - 32 * 24 * 60 * 60 * 1000), description: "VPN Service - April 2025", amount: -9.99, balance: 5.2 }, { date: new Date(Date.now() - 62 * 24 * 60 * 60 * 1000), description: "Initial deposit", amount: 15.19, balance: 15.19 }] as transaction}
        <div class="transaction-card">
          <div class="transaction-header">
            <div class="transaction-date">
              {transaction.date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" })}
            </div>
            <div class="transaction-amount {transaction.amount > 0 ? 'positive' : 'negative'}">
              {transaction.amount > 0 ? "+" : ""}{transaction.amount.toFixed(2)}
            </div>
          </div>
          <div class="transaction-body">
            <div class="transaction-description">
              {transaction.description}
            </div>
            <div class="transaction-balance">
              Balance: ${transaction.balance.toFixed(2)}
            </div>
          </div>
        </div>
      {/each}
    </div>
  </div>
</div>