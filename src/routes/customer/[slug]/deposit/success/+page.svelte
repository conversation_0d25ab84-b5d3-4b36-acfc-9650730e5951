<script>
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import "../../customer.css";

  /** @type {import('./$types').PageData} */
  export let data;
  const { deposit, team, deviceIp } = data;

  let bgLoaded = false;

  onMount(() => {
    /** @param {Event} e */
    const handleTouchMove = (e) => {
      // Cast target to HTMLElement to access tagName
      const target = /** @type {HTMLElement} */ (e.target);
      if (target.tagName !== "INPUT" && target.tagName !== "BUTTON") {
        e.preventDefault();
      }
    };
    
    // Add touchmove event listener with proper typing
    document.body.addEventListener("touchmove", handleTouchMove, false);
    
    return () => {
      document.body.removeEventListener("touchmove", handleTouchMove, false);
    };
  });

  function formatDate(dateString) {
    return new Date(dateString).toLocaleString();
  }

  function getStatusColor(status) {
    switch (status) {
      case 'completed':
        return '#10b981'; // green
      case 'pending':
        return '#f59e0b'; // yellow
      case 'failed':
        return '#ef4444'; // red
      default:
        return '#6b7280'; // gray
    }
  }

  function getStatusText(status) {
    switch (status) {
      case 'completed':
        return 'Payment Completed';
      case 'pending':
        return 'Payment Pending';
      case 'failed':
        return 'Payment Failed';
      default:
        return 'Unknown Status';
    }
  }
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Deposit Status - Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
        <h1>Deposit Status</h1>
      </div>
    </div>
  </div>

  <div class="deposit-card">
    <div class="status-section">
      <div class="status-indicator" style="background-color: {getStatusColor(deposit.status)}">
        {#if deposit.status === 'completed'}
          ✓
        {:else if deposit.status === 'pending'}
          ⏳
        {:else if deposit.status === 'failed'}
          ✗
        {:else}
          ?
        {/if}
      </div>
      <h2 style="color: {getStatusColor(deposit.status)}">{getStatusText(deposit.status)}</h2>
    </div>

    <div class="deposit-details">
      <div class="detail-row">
        <span class="label">Amount:</span>
        <span class="value">${deposit.amount} {deposit.currency}</span>
      </div>
      
      <div class="detail-row">
        <span class="label">Deposit ID:</span>
        <span class="value">{deposit.id}</span>
      </div>
      
      <div class="detail-row">
        <span class="label">Created:</span>
        <span class="value">{formatDate(deposit.createdAt)}</span>
      </div>
      
      {#if deposit.paymentReceivedAt}
        <div class="detail-row">
          <span class="label">Completed:</span>
          <span class="value">{formatDate(deposit.paymentReceivedAt)}</span>
        </div>
      {/if}
      
      {#if deposit.transactionId}
        <div class="detail-row">
          <span class="label">Transaction ID:</span>
          <span class="value">{deposit.transactionId}</span>
        </div>
      {/if}
      
      <div class="detail-row">
        <span class="label">Current Balance:</span>
        <span class="value">${team.balance}</span>
      </div>
    </div>

    {#if deposit.status === 'completed'}
      <div class="success-message">
        <p>Your deposit has been successfully processed and added to your account balance.</p>
      </div>
    {:else if deposit.status === 'pending'}
      <div class="pending-message">
        <p>Your payment is being processed. This page will update automatically when the payment is confirmed.</p>
      </div>
    {:else if deposit.status === 'failed'}
      <div class="error-message">
        <p>Your payment could not be processed. Please try again or contact support.</p>
      </div>
    {/if}
  </div>

  <div class="action-buttons">
    <button class="back-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}`)}>
      ← Back to Dashboard
    </button>
    
    {#if deposit.status === 'failed'}
      <button class="retry-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}/deposit`)}>
        Try Again
      </button>
    {/if}
  </div>
</div>

<style>
  .status-section {
    text-align: center;
    margin-bottom: 2rem;
  }

  .status-indicator {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
  }

  .deposit-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border);
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .label {
    color: var(--text-secondary);
    font-weight: 500;
  }

  .value {
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
    word-break: break-all;
  }

  .success-message {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .success-message p {
    color: #10b981;
    margin: 0;
    text-align: center;
  }

  .pending-message {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .pending-message p {
    color: #f59e0b;
    margin: 0;
    text-align: center;
  }

  .error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: var(--radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .error-message p {
    color: #ef4444;
    margin: 0;
    text-align: center;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .retry-button {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .retry-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
  }

  .retry-button:active {
    transform: translateY(0);
  }
</style>
