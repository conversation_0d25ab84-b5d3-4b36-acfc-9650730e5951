import { error } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase.js';

export async function load({ params, url }) {
  const depositId = url.searchParams.get('deposit_id');
  
  if (!depositId) {
    throw error(400, 'Missing deposit ID');
  }

  try {
    // Get deposit information
    const { data: depositData, error: depositError } = await supabase
      .from('deposits')
      .select(`
        *,
        teams!inner(id, balance)
      `)
      .eq('internal_id', depositId)
      .single();

    if (depositError || !depositData) {
      console.error('[Deposit Success] Deposit not found:', depositError);
      throw error(404, 'Deposit not found');
    }

    return {
      deposit: {
        id: depositData.internal_id,
        amount: depositData.amount,
        currency: depositData.currency,
        status: depositData.payment_status,
        createdAt: depositData.created_at,
        paymentReceivedAt: depositData.payment_received_at,
        transactionId: depositData.payment_transaction_id
      },
      team: {
        id: depositData.teams.id,
        balance: depositData.teams.balance
      },
      deviceIp: params.slug
    };
  } catch (err) {
    console.error('[Deposit Success] Error loading deposit:', err);
    throw error(500, 'Failed to load deposit information');
  }
}
