<script>
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import { enhance } from "$app/forms";
  import { page } from '$app/stores';
  import "../customer.css";
  import toastStore from '$lib/stores/toastStore';

  /** @type {import('./$types').PageData} */
  export let data;
  const { team, deviceIp } = data;

  let amount = "";
  let bgLoaded = false;
  /** @type {HTMLInputElement | null} */
  let amountInput = null;

  onMount(() => {
    // Focus the amount input when the component mounts
    if (amountInput) {
      amountInput.focus({ preventScroll: true });
    }
    
    /** @param {Event} e */
    const handleTouchMove = (e) => {
      // Cast target to HTMLElement to access tagName
      const target = /** @type {HTMLElement} */ (e.target);
      if (target.tagName !== "INPUT" && target.tagName !== "BUTTON") {
        e.preventDefault();
      }
    };
    
    // Add touchmove event listener with proper typing
    document.body.addEventListener("touchmove", handleTouchMove, false);
    
    return () => {
      document.body.removeEventListener("touchmove", handleTouchMove, false);
    };
  });

  // Handle form submission result
  $: if ($page.form?.success) {
    // Show payment options if WestWallet invoice is available
    if ($page.form.westwalletInvoice) {
      // Show cryptocurrency payment options
      showPaymentOptions = true;
    } else {
      // Redirect to payment URL for fallback
      window.location.href = $page.form.paymentUrl;
    }
  } else if ($page.form?.error) {
    toastStore.add($page.form.error, 'error');
  }

  let showPaymentOptions = false;
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Deposit - Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
        <h1>Add Funds</h1>
      </div>
    </div>
  </div>

  <div class="deposit-card">
    {#if !showPaymentOptions}
      <form method="POST" use:enhance>
        <div class="form-group">
          <label for="amount">Amount (USD)</label>
          <input
            type="number"
            id="amount"
            name="amount"
            bind:value={amount}
            placeholder="0.00"
            min="0.01"
            step="0.01"
            inputmode="decimal"
            bind:this={amountInput}
            disabled={$page.form?.success}
            aria-label="Deposit amount in USD"
            required
          />
        </div>

        <div class="quick-amounts">
          {#each [50, 100, 150, 300] as quickAmount}
            <button
              type="button"
              class="quick-amount"
              class:selected={amount === quickAmount.toString()}
              on:click|preventDefault={() => {
                amount = quickAmount.toString();
                // Focus the input after setting the value
                if (amountInput) amountInput.focus();
              }}
              disabled={$page.form?.success}
            >
              ${quickAmount}
            </button>
          {/each}
        </div>

        <button
          type="submit"
          class="deposit-button"
          class:loading={$page.form?.success}
          disabled={!amount || $page.form?.success}
          aria-busy={$page.form?.success}
        >
          {#if $page.form?.success}
            Processing...
          {:else}
            Deposit ${amount || '0.00'}
          {/if}
        </button>
      </form>
    {:else}
      <!-- Payment Options -->
      <div class="payment-options">
        <h3>Choose Payment Method</h3>
        <p class="payment-amount">Amount: ${$page.form.amount} USD</p>

        {#if $page.form.westwalletInvoice?.addresses}
          <div class="crypto-options">
            <h4>Pay with Cryptocurrency</h4>
            {#each Object.entries($page.form.westwalletInvoice.addresses) as [currency, addressData]}
              <div class="crypto-option">
                <div class="crypto-header">
                  <span class="crypto-name">{currency}</span>
                  <span class="crypto-amount">{addressData.amount} {currency}</span>
                </div>
                <div class="crypto-address">
                  <input
                    type="text"
                    value={addressData.address}
                    readonly
                    on:click={(e) => e.target.select()}
                  />
                  <button
                    type="button"
                    class="copy-button"
                    on:click={() => {
                      navigator.clipboard.writeText(addressData.address);
                      toastStore.add('Address copied!', 'success');
                    }}
                  >
                    Copy
                  </button>
                </div>
              </div>
            {/each}
          </div>
        {/if}

        <div class="payment-instructions">
          <p>Send the exact amount to the address above. Your deposit will be credited automatically once the payment is confirmed.</p>
          <p><strong>Important:</strong> Only send the specified cryptocurrency to the corresponding address.</p>
        </div>

        <button
          class="payment-url-button"
          on:click={() => window.location.href = $page.form.paymentUrl}
        >
          Open Payment Page
        </button>
      </div>
    {/if}
  </div>

  <button class="back-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}`)}>
    ← Back
  </button>
</div>

<style>
  .payment-options {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .payment-amount {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary);
    text-align: center;
    margin: 0;
  }

  .crypto-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .crypto-options h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-size: 1rem;
  }

  .crypto-option {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .crypto-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .crypto-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
  }

  .crypto-amount {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .crypto-address {
    display: flex;
    gap: 0.5rem;
  }

  .crypto-address input {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-size: 0.85rem;
    padding: 0.75rem;
    font-family: monospace;
  }

  .copy-button {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }

  .copy-button:hover {
    background: var(--primary-hover);
  }

  .payment-instructions {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: var(--radius-md);
    padding: 1rem;
  }

  .payment-instructions p {
    margin: 0 0 0.5rem 0;
    color: #f59e0b;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .payment-instructions p:last-child {
    margin-bottom: 0;
  }

  .payment-url-button {
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .payment-url-button:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
  }

  .payment-url-button:active {
    transform: translateY(0);
  }
</style>