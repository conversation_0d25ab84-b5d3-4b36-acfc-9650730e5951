:root {
  --primary: linear-gradient(to right, #5b6eff, #ec6fcd);
  --text-primary: #ffffff;
  --text-secondary: #aaa;
  --background: #121212;
  --surface: #1e1e1e;
  --border: #333;
  --radius-md: 8px;
  --transition-normal: 0.3s ease;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  font-size: 16px;
  background: var(--background);
  color: var(--text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.bg-image-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
  opacity: 0.7;
  background: linear-gradient(180deg, rgba(18, 18, 18, 0) 0%, #121212 100%);
}

.bg-image {
  width: 150%;
  height: auto;
  object-fit: contain;
  object-position: bottom center;
  opacity: 0.5;
  filter: blur(4px);
}

.bg-image-container.loaded {
  opacity: 0.7;
}

.app-container {
  width: 100%;
  max-width: 460px;
  margin: 0 auto;
  padding-top: 1rem;
  padding-left: 0.3rem;
  padding-right: 0.3rem;
  padding-bottom: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.7rem;
}

.header-card, .balance-card, .next-charge-card, .billing-card {
  background: linear-gradient(to bottom, #1e1e1e, #121212);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.header {
  text-align: left;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.title-container h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.ip-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
}

.ip-address {
  font-family: monospace;
  color: var(--text-primary);
}

.logo-image {
  width: 36px;
  height: 36px;
  border-radius: 4px;
}

h1 {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
}

.header-actions {
  display: none;
}

.info-icon {
  font-size: 1rem;
  color: var(--primary);
}

.info-label {
  font-size: 0.7rem;
  color: var(--text-secondary);
  text-transform: uppercase;
}

.info-value {
  font-size: 0.85rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
}

.balance-label, .next-charge-label, .section-title {
  color: var(--text-secondary);
  font-size: 1rem;
  text-transform: uppercase;
}

.balance-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary);
}

.action-button {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-family: inherit;
}

.action-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  text-decoration: none;
}

.balance-card .action-button {
  width: 100%;
  margin-top: 0rem;
  display: block;
}

.balance-card a {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-align: center;
  text-decoration: none;
  display: block;
  font-family: inherit;
}

.balance-card a:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  text-decoration: none;
}

.next-charge-amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
}

.next-charge-amount {
  font-size: 1rem;
  font-weight: 500;
  color: var(--primary);
  background: rgba(91, 110, 255, 0.1);
  padding: 0.3rem 0.5rem;
  border-radius: 4px;
  line-height: 1.2;
}

.next-charge-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.progress-container {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.progress-bar {
  height: 100%;
  background: var(--primary);
}

/* Mobile-optimized transaction cards */
.transactions-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.transaction-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 0.7rem;
  transition: all var(--transition-normal);
}

.transaction-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(91, 110, 255, 0.3);
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.transaction-date {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.transaction-amount {
  font-size: 1.1rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  min-width: 60px;
  text-align: right;
}

.transaction-amount.positive {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
}

.transaction-amount.negative {
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

.transaction-body {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.transaction-description {
  font-size: 1rem;
  color: var(--text-primary);
  font-weight: 500;
  line-height: 1.3;
}

.transaction-balance {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  bottom: 1.5rem;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0 1rem;
  pointer-events: none;
  box-sizing: border-box;
}

.toast-message {
  background: var(--surface);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 1.25rem 1.5rem;
  font-size: 1rem;
  line-height: 1.4;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  pointer-events: auto;
  cursor: pointer;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  width: 100%;
  max-width: 400px;
  text-align: center;
  font-weight: 500;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 400px) {
  .toast-message {
    max-width: calc(100% - 2rem);
    padding: 1rem 1.25rem;
  }
}

.toast-message.visible {
  opacity: 1;
  transform: translateY(0);
  animation: toastAppear 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes toastAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.toast-message.toast-error {
  border-left: 4px solid #ff4d4f;
  background: rgba(40, 15, 15, 0.7);
  color: #ff9e9e;
  border-color: #ff4d4f;
}

.toast-message.toast-success {
  border-left: 4px solid #52c41a;
  background: rgba(15, 40, 20, 0.7);
  color: #a7e89c;
  border-color: #52c41a;
}

.toast-message + .toast-message {
  margin-top: 0.5rem;
}

@media (hover: hover) {
  .toast-message:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
  }
  
  .toast-message.visible:hover {
    transform: translateY(-2px);
  }
}

.deposit-card {
  background: linear-gradient(to bottom, #1e1e1e, #121212);
  border-radius: var(--radius-md);
  border: 1px solid var(--border);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

input[type="number"] {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1.5rem;
  padding: 1rem;
  text-align: center;
  width: 100%;
  -moz-appearance: textfield;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quick-amounts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin: 0.5rem 0;
}

.quick-amount {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  padding: 0.75rem;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-amount:hover,
.quick-amount.selected {
  background: var(--primary);
  border-color: var(--primary);
}

.quick-amount:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.deposit-button {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.deposit-button:disabled {
  background: #555;
  cursor: not-allowed;
  opacity: 0.7;
}

.deposit-button.loading {
  background: #555;
  cursor: wait;
}

.back-button {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  color: var(--text-primary);
  border-radius: 12px;
  padding: 16px 20px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  width: 100%;
  margin: 20px 0 0;
  transition: all 0.2s ease;
  margin-top: 0;
  -webkit-tap-highlight-color: transparent;
}

.back-button:active {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(0.98);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.form-group input[type="number"] {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 1.1rem;
  transition: all var(--transition-normal);
}

.form-group input[type="number"]:focus {
  outline: none;
  border-color: #5b6eff;
  box-shadow: 0 0 0 2px rgba(91, 110, 255, 0.25);
}

.quick-amounts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.quick-amount {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.quick-amount:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #5b6eff;
}

.quick-amount.selected {
  background: var(--primary);
  border-color: transparent;
  color: white;
}

.deposit-button {
  width: 100%;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 1rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.deposit-button:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.deposit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.deposit-button.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.error-message {
  background: rgba(255, 72, 72, 0.1);
  border: 1px solid rgba(255, 72, 72, 0.3);
  color: #ff4848;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  text-align: center;
}

.success-message {
  background: rgba(72, 255, 72, 0.1);
  border: 1px solid rgba(72, 255, 72, 0.3);
  color: #48ff48;
  padding: 0.75rem;
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  text-align: center;
}