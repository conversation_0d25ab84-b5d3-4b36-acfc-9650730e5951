
<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>WestWallet API Docs</title>

    <style>
      .highlight table td { padding: 5px; }
.highlight table pre { margin: 0; }
.highlight .gh {
  color: #999999;
}
.highlight .sr {
  color: #f6aa11;
}
.highlight .go {
  color: #888888;
}
.highlight .gp {
  color: #555555;
}
.highlight .gs {
}
.highlight .gu {
  color: #aaaaaa;
}
.highlight .nb {
  color: #f6aa11;
}
.highlight .cm {
  color: #75715e;
}
.highlight .cp {
  color: #75715e;
}
.highlight .c1 {
  color: #75715e;
}
.highlight .cs {
  color: #75715e;
}
.highlight .c, .highlight .ch, .highlight .cd, .highlight .cpf {
  color: #75715e;
}
.highlight .err {
  color: #960050;
}
.highlight .gr {
  color: #960050;
}
.highlight .gt {
  color: #960050;
}
.highlight .gd {
  color: #49483e;
}
.highlight .gi {
  color: #49483e;
}
.highlight .ge {
  color: #49483e;
}
.highlight .kc {
  color: #66d9ef;
}
.highlight .kd {
  color: #66d9ef;
}
.highlight .kr {
  color: #66d9ef;
}
.highlight .no {
  color: #66d9ef;
}
.highlight .kt {
  color: #66d9ef;
}
.highlight .mf {
  color: #ae81ff;
}
.highlight .mh {
  color: #ae81ff;
}
.highlight .il {
  color: #ae81ff;
}
.highlight .mi {
  color: #ae81ff;
}
.highlight .mo {
  color: #ae81ff;
}
.highlight .m, .highlight .mb, .highlight .mx {
  color: #ae81ff;
}
.highlight .sc {
  color: #ae81ff;
}
.highlight .se {
  color: #ae81ff;
}
.highlight .ss {
  color: #ae81ff;
}
.highlight .sd {
  color: #e6db74;
}
.highlight .s2 {
  color: #e6db74;
}
.highlight .sb {
  color: #e6db74;
}
.highlight .sh {
  color: #e6db74;
}
.highlight .si {
  color: #e6db74;
}
.highlight .sx {
  color: #e6db74;
}
.highlight .s1 {
  color: #e6db74;
}
.highlight .s, .highlight .sa, .highlight .dl {
  color: #e6db74;
}
.highlight .na {
  color: #a6e22e;
}
.highlight .nc {
  color: #a6e22e;
}
.highlight .nd {
  color: #a6e22e;
}
.highlight .ne {
  color: #a6e22e;
}
.highlight .nf, .highlight .fm {
  color: #a6e22e;
}
.highlight .vc {
  color: #ffffff;
  background-color: #272822;
}
.highlight .nn {
  color: #ffffff;
  background-color: #272822;
}
.highlight .nl {
  color: #ffffff;
  background-color: #272822;
}
.highlight .ni {
  color: #ffffff;
  background-color: #272822;
}
.highlight .bp {
  color: #ffffff;
  background-color: #272822;
}
.highlight .vg {
  color: #ffffff;
  background-color: #272822;
}
.highlight .vi {
  color: #ffffff;
  background-color: #272822;
}
.highlight .nv, .highlight .vm {
  color: #ffffff;
  background-color: #272822;
}
.highlight .w {
  color: #ffffff;
  background-color: #272822;
}
.highlight {
  color: #ffffff;
  background-color: #272822;
}
.highlight .n, .highlight .py, .highlight .nx {
  color: #ffffff;
  background-color: #272822;
}
.highlight .ow {
  color: #f92672;
}
.highlight .nt {
  color: #f92672;
}
.highlight .k, .highlight .kv {
  color: #f92672;
}
.highlight .kn {
  color: #f92672;
}
.highlight .kp {
  color: #f92672;
}
.highlight .o {
  color: #f92672;
}
    </style>
    <link href="../stylesheets/screen-bcd3675c.css" rel="stylesheet" media="screen" />
    <link href="../stylesheets/print-c427a123.css" rel="stylesheet" media="print" />
      <script src="../javascripts/all-c5541673.js"></script>
  </head>

  <body class="localizable localizable_index" data-languages="[&quot;json&quot;,&quot;python&quot;,&quot;javascript&quot;,&quot;go&quot;,&quot;php&quot;]">
    <a href="#" id="nav-button">
      <span>
        NAV
        <img src="../images/navbar-cad8cdcb.png" alt="" />
      </span>
    </a>
    <div class="toc-wrapper">

      <a href="https://westwallet.io/"><img src="../images/logo-1c706ba8.png" class="logo" alt="" /></a>
        <div class="lang-selector">
              <a href="#" data-language-name="json">json</a>
              <a href="#" data-language-name="python">python</a>
              <a href="#" data-language-name="javascript">javascript</a>
              <a href="#" data-language-name="go">go</a>
              <a href="#" data-language-name="php">php</a>
        </div>
        <div class="search">
          <input type="text" class="search" id="input-search" placeholder="Search">
        </div>
        <ul class="search-results"></ul>
      <ul id="toc" class="toc-list-h1">
          <li>
            <a href="#before-start" class="toc-h1 toc-link" data-title="Before start">Before start</a>
          </li>
          <li>
            <a href="#authorization" class="toc-h1 toc-link" data-title="Authorization">Authorization</a>
          </li>
          <li>
            <a href="#wallet" class="toc-h1 toc-link" data-title="Wallet">Wallet</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#info-about-currencies" class="toc-h2 toc-link" data-title="Info about currencies">Info about currencies</a>
                  </li>
                  <li>
                    <a href="#withdrawals" class="toc-h2 toc-link" data-title="Withdrawals">Withdrawals</a>
                  </li>
                  <li>
                    <a href="#sending-multiple-transactions" class="toc-h2 toc-link" data-title="Sending multiple transactions">Sending multiple transactions</a>
                  </li>
                  <li>
                    <a href="#transaction-status" class="toc-h2 toc-link" data-title="Transaction status">Transaction status</a>
                  </li>
                  <li>
                    <a href="#transactions-history" class="toc-h2 toc-link" data-title="Transactions history">Transactions history</a>
                  </li>
                  <li>
                    <a href="#wallet-balance" class="toc-h2 toc-link" data-title="Wallet balance">Wallet balance</a>
                  </li>
                  <li>
                    <a href="#all-wallet-39-s-balances" class="toc-h2 toc-link" data-title="All wallet's balances">All wallet's balances</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#addresses" class="toc-h1 toc-link" data-title="Addresses">Addresses</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#generate-address" class="toc-h2 toc-link" data-title="Generate address">Generate address</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#payment-page-invoice" class="toc-h1 toc-link" data-title="Payment page (invoice)">Payment page (invoice)</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#http-request-9" class="toc-h2 toc-link" data-title="HTTP request">HTTP request</a>
                  </li>
                  <li>
                    <a href="#post-params-6" class="toc-h2 toc-link" data-title="Post params">Post params</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#list-of-transactions-of-the-payment-page-invoice" class="toc-h1 toc-link" data-title="List of transactions of the payment page (invoice)">List of transactions of the payment page (invoice)</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#http-request-10" class="toc-h2 toc-link" data-title="HTTP request">HTTP request</a>
                  </li>
                  <li>
                    <a href="#query-params-2" class="toc-h2 toc-link" data-title="Query params">Query params</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#notifications-about-payments" class="toc-h1 toc-link" data-title="Notifications about payments">Notifications about payments</a>
              <ul class="toc-list-h2">
                  <li>
                    <a href="#receipt-transaction-notifications-incoming-transactions" class="toc-h2 toc-link" data-title="Receipt transaction notifications (incoming transactions)">Receipt transaction notifications (incoming transactions)</a>
                  </li>
                  <li>
                    <a href="#notifications-about-sending-transactions-outgoing-transactions" class="toc-h2 toc-link" data-title="Notifications about sending transactions (outgoing transactions)">Notifications about sending transactions (outgoing transactions)</a>
                  </li>
              </ul>
          </li>
          <li>
            <a href="#possible-errors" class="toc-h1 toc-link" data-title="Possible errors">Possible errors</a>
          </li>
          <li>
            <a href="#errors" class="toc-h1 toc-link" data-title="Errors">Errors</a>
          </li>
      </ul>
        <ul class="toc-footer">
			<li><a href='https://westwallet.io'>Return to website</a></li>
        </ul>
    </div>
    <div class="page-wrapper">
      <div class="dark-box"></div>
      <div class="content">
        <h1 id='before-start'>Before start</h1>
<p>Welcome to the official WestWallet API documentation.</p>

<p>Change docs language:
<a href="/uk">UA</a>
<a href="/en">EN</a>
<a href="/de">DE</a>
<a href="/ru">RU</a></p>

<aside class="notice">
<strong>Attention</strong> We are sending instant payment notifications (IPN) only from IPs: <code>***********</code> !
</aside>

<p><a href="https://westwallet.io/settings">Authorize</a> to get an API-key in the profile settings.</p>

<p>Libraries for programming languages:
<a href="https://github.com/WestWallet/westwallet-python-api">Python</a>
<a href="https://github.com/WestWallet/westwallet-js-api">JavaScript</a>
<a href="https://github.com/WestWallet/westwallet-golang-api">Golang</a>
<a href="https://github.com/WestWallet/westwallet-php-api">PHP</a></p>
<h1 id='authorization'>Authorization</h1><div class="highlight"><pre class="highlight python tab-python"><code><span class="kn">import</span> <span class="nn">json</span>
<span class="kn">import</span> <span class="nn">hashlib</span>
<span class="kn">import</span> <span class="nn">hmac</span>
<span class="kn">import</span> <span class="nn">time</span>
<span class="kn">import</span> <span class="nn">requests</span>

<span class="c1"># Get transaction info
</span><span class="n">api_key</span> <span class="o">=</span> <span class="s">"your_public_key"</span>
<span class="n">secret_key</span> <span class="o">=</span> <span class="s">"your_private_key"</span>
<span class="n">data</span> <span class="o">=</span> <span class="p">{</span><span class="s">"id"</span><span class="p">:</span> <span class="mi">435</span><span class="p">}</span>
<span class="n">timestamp</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">time</span><span class="p">.</span><span class="n">time</span><span class="p">())</span>
<span class="k">if</span> <span class="n">data</span><span class="p">:</span>
    <span class="n">dumped</span> <span class="o">=</span> <span class="n">json</span><span class="p">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="n">ensure_ascii</span><span class="o">=</span><span class="bp">False</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="n">dumped</span> <span class="o">=</span> <span class="s">""</span>
<span class="n">sign</span> <span class="o">=</span> <span class="n">hmac</span><span class="p">.</span><span class="n">new</span><span class="p">(</span><span class="n">secret_key</span><span class="p">.</span><span class="n">encode</span><span class="p">(</span><span class="s">'utf-8'</span><span class="p">),</span>
                <span class="s">"{}{}"</span><span class="p">.</span><span class="nb">format</span><span class="p">(</span><span class="n">timestamp</span><span class="p">,</span> <span class="n">dumped</span><span class="p">)</span>
                <span class="p">.</span><span class="n">encode</span><span class="p">(</span><span class="s">'utf-8'</span><span class="p">),</span> <span class="n">hashlib</span><span class="p">.</span><span class="n">sha256</span><span class="p">).</span><span class="n">hexdigest</span><span class="p">()</span>
<span class="n">headers</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s">"Content-Type"</span><span class="p">:</span> <span class="s">"application/json"</span><span class="p">,</span>
    <span class="s">"X-API-KEY"</span><span class="p">:</span> <span class="n">api_key</span><span class="p">,</span>
    <span class="s">"X-ACCESS-SIGN"</span><span class="p">:</span> <span class="n">sign</span><span class="p">,</span>
    <span class="s">"X-ACCESS-TIMESTAMP"</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">timestamp</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">resp</span> <span class="o">=</span> <span class="n">requests</span><span class="p">.</span><span class="n">post</span><span class="p">(</span><span class="s">"https://api.westwallet.io/wallet/transaction"</span><span class="p">,</span> 
                     <span class="n">data</span><span class="o">=</span><span class="n">json</span><span class="p">.</span><span class="n">dumps</span><span class="p">(</span><span class="n">data</span><span class="p">),</span>
                     <span class="n">headers</span><span class="o">=</span><span class="n">headers</span><span class="p">)</span>
<span class="k">print</span><span class="p">(</span><span class="n">resp</span><span class="p">.</span><span class="n">json</span><span class="p">())</span>                     
</code></pre></div>
<p>WestWallet API expects to receive <code>X-API-KEY</code>, <code>X-ACCESS-SIGN</code> and <code>X-ACCESS-TIMESTAMP</code> in the header of each of your request. </p>

<p><code>X-API-KEY</code> - your public key;</p>

<p><code>X-ACCESS-TIMESTAMP</code> - timestamp (use unix timestamp);</p>

<p><code>X-ACCESS-SIGN</code> - HMAC-sha256 request body signature (composed of lines, 
comprising the timestamp and JSON-representation of the request body data) 
signed by your private key. For GET requests it should be made 
with JSON-dump of query parameters.</p>

<p>See an example how to form a string for generating signature at <a href="/en/?python#authorization">Python examples</a></p>
<h1 id='wallet'>Wallet</h1><h2 id='info-about-currencies'>Info about currencies</h2>
<blockquote>
<p><code>GET https://api.westwallet.io/wallet/currencies_data</code></p>
</blockquote>

<p>Request to get list of available currencies and their limits.</p>

<ul>
<li>name - name of currency</li>
<li>address_regex - regular expression used to validate address before creating send transaction</li>
<li>require_dest_tag - shows if currency require destination tag for receive or send transactions</li>
<li>tickers - list of allowed tickers of currency to pass in requests</li>
<li>min_receive - minimal amount for deposits</li>
<li>min_withdraw - minimal withdrawal amount</li>
<li>max_withdraw_per_transaction - maximal withdrawal amount per 1 transaction</li>
<li>max_withdraw_transactions_per_day - maximal withdrawal transactions per day</li>
<li>active - whether the currency is active</li>
<li>send_active - whether withdrawal is allowed for the currency</li>
<li>receive_active - whether receiving is allowed for the currency</li>
</ul>
<h3 id='http-request'>HTTP request</h3>
<p><code>GET /wallet/currencies_data</code></p>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">[</span><span class="w">
  </span><span class="p">{</span><span class="w">
    </span><span class="nl">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"Bitcoin"</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"address_regex"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^([13][a-km-zA-HJ-NP-Z1-9]{25,34})|^(bc1([qpzry9x8gf2tvdw0s3jn54khce6mua7l]{39}|[qpzry9x8gf2tvdw0s3jn54khce6mua7l]{59}))$"</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"require_dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"tickers"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
    </span><span class="p">],</span><span class="w"> 
    </span><span class="nl">"min_receive"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0005</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"min_withdraw"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.0001</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"max_withdraw_per_transaction"</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"max_withdraw_transactions_per_day"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000000</span><span class="p">,</span><span class="w">
    </span><span class="nl">"active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"send_active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"receive_active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="p">},</span><span class="w"> 
  </span><span class="p">{</span><span class="w">
    </span><span class="nl">"name"</span><span class="p">:</span><span class="w"> </span><span class="s2">"USDT TRC-20"</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"address_regex"</span><span class="p">:</span><span class="w"> </span><span class="s2">"^[T][a-km-zA-HJ-NP-Z1-9]{25,34}$"</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"require_dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"tickers"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="s2">"USDTTRC"</span><span class="p">,</span><span class="w">
    </span><span class="p">],</span><span class="w"> 
    </span><span class="nl">"min_receive"</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"min_withdraw"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"max_withdraw_per_transaction"</span><span class="p">:</span><span class="w"> </span><span class="mi">100000</span><span class="p">,</span><span class="w"> 
    </span><span class="nl">"max_withdraw_transactions_per_day"</span><span class="p">:</span><span class="w"> </span><span class="mi">1000000</span><span class="p">,</span><span class="w">
    </span><span class="nl">"active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"send_active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span><span class="w">
    </span><span class="nl">"receive_active"</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="w">
  </span><span class="p">}</span><span class="w">
</span><span class="p">]</span><span class="w">
</span></code></pre></div><h2 id='withdrawals'>Withdrawals</h2>
<blockquote>
<p><code>POST https://api.westwallet.io/wallet/create_withdrawal</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">123123</span><span class="p">,</span><span class="w">
  </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pending"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"72648cefcc47b4371f28dc3328bc863918913eebf81b40d4a97d577b96c1ce53"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0001"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight python tab-python"><code><span class="c1"># Send 0.1 ETH to ******************************************
</span><span class="kn">from</span> <span class="nn">westwallet_api</span> <span class="kn">import</span> <span class="n">WestWalletAPI</span>
<span class="kn">from</span> <span class="nn">westwallet_api.exceptions</span> <span class="kn">import</span> <span class="n">InsufficientFundsException</span><span class="p">,</span> <span class="n">BadAddressException</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">WestWalletAPI</span><span class="p">(</span><span class="s">"your_public_key"</span><span class="p">,</span> <span class="s">"your_private_key"</span><span class="p">)</span>
<span class="k">try</span><span class="p">:</span>
    <span class="n">sent_transaction</span> <span class="o">=</span> <span class="n">client</span><span class="p">.</span><span class="n">create_withdrawal</span><span class="p">(</span>
        <span class="s">"ETH"</span><span class="p">,</span> <span class="s">"0.1"</span><span class="p">,</span> <span class="s">"******************************************"</span>
    <span class="p">)</span>
<span class="k">except</span> <span class="n">InsufficientFundsException</span><span class="p">:</span>
    <span class="c1"># handle this case
</span>    <span class="k">pass</span>
<span class="k">except</span> <span class="n">BadAddressException</span><span class="p">:</span>
    <span class="c1"># handle also this case
</span>    <span class="k">pass</span>
<span class="k">else</span><span class="p">:</span>
    <span class="k">print</span><span class="p">(</span><span class="n">sent_transaction</span><span class="p">.</span><span class="n">__dict__</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight javascript tab-javascript"><code><span class="kd">const</span> <span class="nx">westwallet</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">westwalletErrors</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api/lib/errors</span><span class="dl">'</span><span class="p">);</span>

<span class="kd">const</span> <span class="nx">publicKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPublicKey</span><span class="dl">"</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">privateKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPrivateKey</span><span class="dl">"</span><span class="p">;</span>

<span class="kd">let</span> <span class="nx">client</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">westwallet</span><span class="p">.</span><span class="nx">WestWalletAPI</span><span class="p">(</span>
    <span class="nx">publicKey</span><span class="p">,</span> 
    <span class="nx">privateKey</span>
<span class="p">);</span>

<span class="nx">client</span><span class="p">.</span><span class="nx">createWithdrawal</span><span class="p">(</span><span class="dl">"</span><span class="s2">ETH</span><span class="dl">"</span><span class="p">,</span> <span class="dl">"</span><span class="s2">0.1</span><span class="dl">"</span><span class="p">,</span> <span class="dl">"</span><span class="s2">******************************************</span><span class="dl">"</span><span class="p">)</span>
<span class="p">.</span><span class="nx">then</span><span class="p">((</span><span class="nx">data</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="k">if</span> <span class="p">(</span><span class="nx">error</span> <span class="k">instanceof</span> <span class="nx">westwalletErrors</span><span class="p">.</span><span class="nx">InsufficientFundsError</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="dl">"</span><span class="s2">Insufficient funds</span><span class="dl">"</span><span class="p">);</span>
    <span class="p">}</span> <span class="k">else</span> <span class="k">if</span> <span class="p">(</span><span class="nx">error</span> <span class="k">instanceof</span> <span class="nx">westwalletErrors</span><span class="p">.</span><span class="nx">BadAddressError</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="dl">"</span><span class="s2">Bad address regex</span><span class="dl">"</span><span class="p">);</span>
    <span class="p">}</span> <span class="k">else</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">});</span>
</code></pre></div><div class="highlight"><pre class="highlight go tab-go"><code><span class="k">package</span> <span class="n">main</span>

<span class="k">import</span> <span class="p">(</span>
    <span class="s">"fmt"</span>
    <span class="n">westwallet</span> <span class="s">"github.com/westwallet/westwallet-golang-api"</span>
<span class="p">)</span>

<span class="c">// Sending 0.1 ETH to ******************************************</span>
<span class="n">client</span> <span class="o">:=</span> <span class="n">westwallet</span><span class="o">.</span><span class="n">APIClient</span><span class="p">{</span>
    <span class="n">Key</span><span class="o">:</span>      <span class="s">"your_public_key"</span><span class="p">,</span>
    <span class="n">Secret</span><span class="o">:</span>   <span class="s">"your_private_key"</span><span class="p">,</span>
<span class="p">}</span>

<span class="n">transaction</span><span class="p">,</span> <span class="n">err</span> <span class="o">:=</span> <span class="n">client</span><span class="o">.</span><span class="n">CreateWithdrawal</span><span class="p">(</span>
    <span class="s">"ETH"</span><span class="p">,</span> <span class="s">"0.1"</span><span class="p">,</span> <span class="s">"******************************************"</span><span class="p">,</span> <span class="s">""</span><span class="p">,</span> <span class="s">""</span>
<span class="p">)</span>

<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="k">if</span> <span class="n">err</span> <span class="o">!=</span> <span class="no">nil</span> <span class="p">{</span>
    <span class="nb">panic</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">transaction</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\InsufficientFundsException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>

<span class="c1">// Send 0.1 ETH to ******************************************</span>
<span class="k">try</span> <span class="p">{</span>
    <span class="nv">$tx</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">createWithdrawal</span><span class="p">(</span><span class="s2">"ETH"</span><span class="p">,</span> <span class="s2">"0.1"</span><span class="p">,</span> <span class="s2">"******************************************"</span><span class="p">);</span>
    <span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$tx</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span> <span class="k">catch</span><span class="p">(</span><span class="nc">InsufficientFundsException</span> <span class="nv">$e</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">print</span><span class="p">(</span><span class="s2">"You don't have enough funds to make this withdrawal"</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<p>Withdraw funds from desired wallet.</p>
<h3 id='http-request-2'>HTTP request</h3>
<p><code>POST /wallet/create_withdrawal</code></p>
<h3 id='post-params'>Post params</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currency</td>
<td>BTC</td>
<td>yes</td>
<td>Currency to be sent</td>
</tr>
<tr>
<td>amount</td>
<td>0.1</td>
<td>yes</td>
<td>Amount to be sent</td>
</tr>
<tr>
<td>address</td>
<td>**********************************</td>
<td>да</td>
<td>Receive address</td>
</tr>
<tr>
<td>dest_tag</td>
<td>1390985919</td>
<td>no</td>
<td>Destination tag (required for some currencies)</td>
</tr>
<tr>
<td>description</td>
<td></td>
<td>no</td>
<td>Label for further identification in dashboard</td>
</tr>
<tr>
<td>priority</td>
<td>medium</td>
<td>no</td>
<td>Transaction priority - low (24-48 hour withdraw interval, available for many currencies, reduced fees that can be discussed when contacting support), medium or high. Default value is medium.</td>
</tr>
<tr>
<td>ipn_url</td>
<td>https://yourwebsite.com/payment_secret_token?tx_id=123321</td>
<td>no</td>
<td>Instant Payment Notification URL. At this URL you will be notified as soon as transaction will be actually sent.</td>
</tr>
</tbody></table>
<h2 id='sending-multiple-transactions'>Sending multiple transactions</h2>
<blockquote>
<p><code>POST https://api.westwallet.io/wallet/create_withdrawal/many</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="err">#</span><span class="w"> </span><span class="err">Request</span><span class="w">
</span><span class="p">{</span><span class="w">
   </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
   </span><span class="nl">"priority"</span><span class="p">:</span><span class="w"> </span><span class="s2">"high"</span><span class="p">,</span><span class="w">
   </span><span class="nl">"transactions"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"5"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40124"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"3KJFvx88XUtqUgannLDZFm7XzHXACGLuJL"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.015"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40125"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.010"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40126"</span><span class="w">
      </span><span class="p">}</span><span class="w">
   </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">

</span><span class="err">#</span><span class="w"> </span><span class="err">Response</span><span class="w">
</span><span class="p">{</span><span class="w">
   </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="p">,</span><span class="w">
   </span><span class="nl">"results"</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"5.00000000"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40124"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"insufficient_funds"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"3KJFvx88XUtqUgannLDZFm7XzHXACGLuJL"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.01500000"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"72648cefcc47b4371f28dc3328bc863918913eebf81b40d4a97d577b96c1ce53"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.00027500"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">611438237</span><span class="p">,</span><span class="w">
         </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pending"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40125"</span><span class="w">
      </span><span class="p">},</span><span class="w">
      </span><span class="p">{</span><span class="w">
         </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.01000000"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"72648cefcc47b4371f28dc3328bc863918913eebf81b40d4a97d577b96c1ce53"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">611438238</span><span class="p">,</span><span class="w">
         </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pending"</span><span class="p">,</span><span class="w">
         </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"40126"</span><span class="w">
      </span><span class="p">}</span><span class="w">
   </span><span class="p">]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div>
<p>Send several transactions from the certain wallet. In this case, fees discounts are applied. </p>

<p>Second and subsequent transactions are subject to a 50% discount for the fixed fee type. </p>

<p>The first transaction is the transaction with the largest amount.</p>

<p>The method works only for currencies with the ability of bulk send: BTC, BCH, BTG, BSV, ADA, DASH, DOGE, LTC, XMR, ZEC.</p>
<h3 id='http-request-3'>HTTP request</h3>
<p><code>POST /wallet/create_withdrawal/many</code></p>
<h3 id='post-params-2'>Post params</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currency</td>
<td>BTC</td>
<td>yes</td>
<td>Currency to be sent</td>
</tr>
<tr>
<td>priority</td>
<td>medium</td>
<td>no</td>
<td>Transaction priority - low (24-48 hour withdraw interval, available for many currencies, reduced fees that can be discussed when contacting support), medium or high. Default value is medium.</td>
</tr>
<tr>
<td>transactions</td>
<td></td>
<td>yes</td>
<td>An array (list) of transaction data. The structure of the fields is given below</td>
</tr>
</tbody></table>
<h3 id='post-parameters-of-the-transactions-structure'>Post parameters of the transactions structure</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>amount</td>
<td>0.1</td>
<td>yes</td>
<td>Amount to be sent</td>
</tr>
<tr>
<td>address</td>
<td>**********************************</td>
<td>да</td>
<td>Receive address</td>
</tr>
<tr>
<td>description</td>
<td></td>
<td>no</td>
<td>Label for further identification in dashboard</td>
</tr>
<tr>
<td>ipn_url</td>
<td>https://yourwebsite.com/payment_secret_token?tx_id=123321</td>
<td>no</td>
<td>Instant Payment Notification URL. At this URL you will be notified as soon as transaction will be actually sent.</td>
</tr>
</tbody></table>
<h2 id='transaction-status'>Transaction status</h2>
<p>Possible statuses:</p>

<ul>
<li>&quot;completed&quot; - transaction was successfully completed.</li>
<li>&quot;pending&quot; - transaction is in processing with other transactions.</li>
<li>&quot;sending&quot; - send action has been done but result is undefined. It&#39;s not final status. Our operator will automatically check it will change status to &quot;completed&quot; or &quot;network_error&quot;.</li>
<li>&quot;network_error&quot; - blockchain error has occured.</li>
</ul>

<blockquote>
<p><code>POST https://api.westwallet.io/wallet/transaction</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">123123</span><span class="p">,</span><span class="w">
  </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"send"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rw2ciyaNshpHe7bCHo4bRWq6pqqynnWKQg"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">"755785168"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"label"</span><span class="p">:</span><span class="w"> </span><span class="s2">"your_label"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"XRP"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"completed"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"blockchain_confirmations"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BC07C0937F2B12D8DF8F90B5A421957DC690DC8512F97925217726E6A28F0A93"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0001"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight python tab-python"><code><span class="kn">from</span> <span class="nn">westwallet_api</span> <span class="kn">import</span> <span class="n">WestWalletAPI</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">WestWalletAPI</span><span class="p">(</span><span class="s">"your_public_key"</span><span class="p">,</span> <span class="s">"your_private_key"</span><span class="p">)</span>
<span class="n">transaction</span> <span class="o">=</span> <span class="n">client</span><span class="p">.</span><span class="n">transaction_info</span><span class="p">(</span><span class="mi">19</span><span class="p">)</span>
<span class="k">print</span><span class="p">(</span><span class="n">transaction</span><span class="p">.</span><span class="n">__dict__</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight javascript tab-javascript"><code><span class="kd">const</span> <span class="nx">westwallet</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">westwalletErrors</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api/lib/errors</span><span class="dl">'</span><span class="p">);</span>

<span class="kd">const</span> <span class="nx">publicKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPublicKey</span><span class="dl">"</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">privateKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPrivateKey</span><span class="dl">"</span><span class="p">;</span>

<span class="kd">let</span> <span class="nx">client</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">westwallet</span><span class="p">.</span><span class="nx">WestWalletAPI</span><span class="p">(</span>
    <span class="nx">publicKey</span><span class="p">,</span> 
    <span class="nx">privateKey</span>
<span class="p">);</span>

<span class="nx">client</span><span class="p">.</span><span class="nx">transactionInfo</span><span class="p">(</span><span class="mi">1284</span><span class="p">).</span><span class="nx">then</span><span class="p">((</span><span class="nx">data</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="k">if</span> <span class="p">(</span><span class="nx">error</span> <span class="k">instanceof</span> <span class="nx">westwalletErrors</span><span class="p">.</span><span class="nx">TransactionNotFoundError</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="dl">"</span><span class="s2">Transaction not found</span><span class="dl">"</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">});</span>
</code></pre></div><div class="highlight"><pre class="highlight go tab-go"><code><span class="k">package</span> <span class="n">main</span>

<span class="k">import</span> <span class="p">(</span>
    <span class="s">"fmt"</span>
    <span class="n">westwallet</span> <span class="s">"github.com/westwallet/westwallet-golang-api"</span>
<span class="p">)</span>

<span class="n">client</span> <span class="o">:=</span> <span class="n">westwallet</span><span class="o">.</span><span class="n">APIClient</span><span class="p">{</span>
    <span class="n">Key</span><span class="o">:</span>      <span class="s">"your_public_key"</span><span class="p">,</span>
    <span class="n">Secret</span><span class="o">:</span>   <span class="s">"your_private_key"</span><span class="p">,</span>
<span class="p">}</span>

<span class="n">transaction</span><span class="p">,</span> <span class="n">err</span> <span class="o">:=</span> <span class="n">client</span><span class="o">.</span><span class="n">TransactionInfo</span><span class="p">(</span><span class="m">145</span><span class="p">);</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="k">if</span> <span class="n">err</span> <span class="o">!=</span> <span class="no">nil</span> <span class="p">{</span>
    <span class="nb">panic</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">transaction</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\TransactionNotFoundException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>

<span class="k">try</span> <span class="p">{</span>
    <span class="nv">$tx</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">transactionInfo</span><span class="p">(</span><span class="mi">134</span><span class="p">);</span>
    <span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$tx</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span> <span class="k">catch</span><span class="p">(</span><span class="nc">TransactionNotFoundException</span> <span class="nv">$e</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">print</span><span class="p">(</span><span class="s2">"Transaction not found"</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<p>Find out the status of the transaction.</p>
<h3 id='http-request-4'>HTTP request</h3>
<p><code>POST /wallet/transaction</code></p>
<h3 id='post-params-3'>Post params</h3>
<table><thead>
<tr>
<th>Arguments</th>
<th>Examples</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>id</td>
<td>123123</td>
<td>id or label</td>
<td>Transaction ID within WestWallet service</td>
</tr>
<tr>
<td>label</td>
<td>321321</td>
<td>id or label</td>
<td>Label of address of receive transaction within WestWallet service</td>
</tr>
</tbody></table>
<h2 id='transactions-history'>Transactions history</h2>
<blockquote>
<p><code>POST https://api.westwallet.io/wallet/transactions</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"result"</span><span class="p">:</span><span class="w"> </span><span class="p">[{</span><span class="w">
    </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">123123</span><span class="p">,</span><span class="w">
    </span><span class="nl">"created_at"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2019-12-29 15:07:13"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"updated_at"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2019-12-29 15:12:43"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"send"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span><span class="w">
    </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rw2ciyaNshpHe7bCHo4bRWq6pqqynnWKQg"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">"755785168"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"label"</span><span class="p">:</span><span class="w"> </span><span class="s2">"your_label"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"XRP"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"completed"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"your custom description"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"blockchain_confirmations"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
    </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BC07C0937F2B12D8DF8F90B5A421957DC690DC8512F97925217726E6A28F0A93"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0001"</span><span class="w">
  </span><span class="p">}]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div>
<p>Check transactions&#39; history.</p>
<h3 id='http-request-5'>HTTP request</h3>
<p><code>POST /wallet/transactions</code></p>
<h3 id='post-params-4'>Post params</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currency</td>
<td>BTC</td>
<td>no</td>
<td>Currency code. If not specified - you&#39;ll get transactions for all currencies based on other request params.</td>
</tr>
<tr>
<td>limit</td>
<td>10</td>
<td>no</td>
<td>Limit of transactions in response (max 100).</td>
</tr>
<tr>
<td>offset</td>
<td>20</td>
<td>no</td>
<td>Offset.</td>
</tr>
<tr>
<td>type</td>
<td>send</td>
<td>no</td>
<td>Transaction type - send or receive. If it&#39;s not specify - you&#39;ll get both of them.</td>
</tr>
<tr>
<td>order</td>
<td>desc</td>
<td>no</td>
<td>Date-time order - descending (desc) or ascending (asc).</td>
</tr>
<tr>
<td>status</td>
<td>pending</td>
<td>no</td>
<td>Status of transactions to filter (pending or completed)</td>
</tr>
</tbody></table>
<h2 id='wallet-balance'>Wallet balance</h2>
<blockquote>
<p><code>GET https://api.westwallet.io/wallet/balance?currency=BTC</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"balance"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.55</span><span class="p">,</span><span class="w">
  </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight python tab-python"><code><span class="kn">from</span> <span class="nn">westwallet_api</span> <span class="kn">import</span> <span class="n">WestWalletAPI</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">WestWalletAPI</span><span class="p">(</span><span class="s">"your_public_key"</span><span class="p">,</span> <span class="s">"your_private_key"</span><span class="p">)</span>
<span class="n">balance</span> <span class="o">=</span> <span class="n">client</span><span class="p">.</span><span class="n">wallet_balance</span><span class="p">(</span><span class="s">"BTC"</span><span class="p">)</span>
<span class="k">print</span><span class="p">(</span><span class="n">balance</span><span class="p">.</span><span class="n">balance</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight javascript tab-javascript"><code><span class="kd">const</span> <span class="nx">westwallet</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">westwalletErrors</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api/lib/errors</span><span class="dl">'</span><span class="p">);</span>

<span class="kd">const</span> <span class="nx">publicKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPublicKey</span><span class="dl">"</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">privateKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPrivateKey</span><span class="dl">"</span><span class="p">;</span>

<span class="kd">let</span> <span class="nx">client</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">westwallet</span><span class="p">.</span><span class="nx">WestWalletAPI</span><span class="p">(</span>
    <span class="nx">publicKey</span><span class="p">,</span> 
    <span class="nx">privateKey</span>
<span class="p">);</span>

<span class="nx">client</span><span class="p">.</span><span class="nx">walletBalance</span><span class="p">(</span><span class="dl">"</span><span class="s2">BTC</span><span class="dl">"</span><span class="p">).</span><span class="nx">then</span><span class="p">((</span><span class="nx">data</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="k">if</span> <span class="p">(</span><span class="nx">error</span> <span class="k">instanceof</span> <span class="nx">westwalletErrors</span><span class="p">.</span><span class="nx">CurrencyNotFoundError</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="dl">"</span><span class="s2">No such currency</span><span class="dl">"</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">});</span>
</code></pre></div><div class="highlight"><pre class="highlight go tab-go"><code><span class="k">package</span> <span class="n">main</span>

<span class="k">import</span> <span class="p">(</span>
    <span class="s">"fmt"</span>
    <span class="n">westwallet</span> <span class="s">"github.com/westwallet/westwallet-golang-api"</span>
<span class="p">)</span>

<span class="n">client</span> <span class="o">:=</span> <span class="n">westwallet</span><span class="o">.</span><span class="n">APIClient</span><span class="p">{</span>
    <span class="n">Key</span><span class="o">:</span>      <span class="s">"your_public_key"</span><span class="p">,</span>
    <span class="n">Secret</span><span class="o">:</span>   <span class="s">"your_private_key"</span><span class="p">,</span>
<span class="p">}</span>

<span class="n">balance</span><span class="p">,</span> <span class="n">err</span> <span class="o">:=</span> <span class="n">client</span><span class="o">.</span><span class="n">WalletBalance</span><span class="p">(</span><span class="s">"BTC"</span><span class="p">);</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="k">if</span> <span class="n">err</span> <span class="o">!=</span> <span class="no">nil</span> <span class="p">{</span>
    <span class="nb">panic</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">balance</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\CurrencyNotFoundException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>

<span class="k">try</span> <span class="p">{</span>
    <span class="nv">$balance</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">walletBalance</span><span class="p">(</span><span class="s2">"BTC"</span><span class="p">);</span>
    <span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$balance</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span> <span class="k">catch</span><span class="p">(</span><span class="nc">CurrencyNotFoundException</span> <span class="nv">$e</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">print</span><span class="p">(</span><span class="s2">"No such currency"</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<p>Check wallet balance</p>
<h3 id='http-request-6'>HTTP request</h3>
<p><code>GET /wallet/balance?currency=BTC</code></p>
<h3 id='query-params'>Query params</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currency</td>
<td>BTC</td>
<td>yes</td>
<td>Wallet currency</td>
</tr>
</tbody></table>
<h2 id='all-wallet-39-s-balances'>All wallet&#39;s balances</h2>
<blockquote>
<p><code>GET https://api.westwallet.io/wallet/balances</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"ETH"</span><span class="p">:</span><span class="w"> </span><span class="mf">3.22</span><span class="p">,</span><span class="w">
  </span><span class="nl">"ETC"</span><span class="p">:</span><span class="w"> </span><span class="mi">34</span><span class="p">,</span><span class="w">
  </span><span class="nl">"LTC"</span><span class="p">:</span><span class="w"> </span><span class="mi">40</span><span class="p">,</span><span class="w">
  </span><span class="nl">"BTC"</span><span class="p">:</span><span class="w"> </span><span class="mf">1.11</span><span class="p">,</span><span class="w">
  </span><span class="nl">"XLM"</span><span class="p">:</span><span class="w"> </span><span class="mf">319.11</span><span class="p">,</span><span class="w">
  </span><span class="nl">"XMR"</span><span class="p">:</span><span class="w"> </span><span class="mf">15.12</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight python tab-python"><code><span class="kn">from</span> <span class="nn">westwallet_api</span> <span class="kn">import</span> <span class="n">WestWalletAPI</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">WestWalletAPI</span><span class="p">(</span><span class="s">"your_public_key"</span><span class="p">,</span> <span class="s">"your_private_key"</span><span class="p">)</span>
<span class="n">balances</span> <span class="o">=</span> <span class="n">client</span><span class="p">.</span><span class="n">wallet_balances</span><span class="p">()</span>
<span class="k">print</span><span class="p">(</span><span class="n">balances</span><span class="p">.</span><span class="n">__dict__</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight javascript tab-javascript"><code><span class="kd">const</span> <span class="nx">westwallet</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">westwalletErrors</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api/lib/errors</span><span class="dl">'</span><span class="p">);</span>

<span class="kd">const</span> <span class="nx">publicKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPublicKey</span><span class="dl">"</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">privateKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPrivateKey</span><span class="dl">"</span><span class="p">;</span>

<span class="kd">let</span> <span class="nx">client</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">westwallet</span><span class="p">.</span><span class="nx">WestWalletAPI</span><span class="p">(</span>
    <span class="nx">publicKey</span><span class="p">,</span> 
    <span class="nx">privateKey</span>
<span class="p">);</span>

<span class="nx">client</span><span class="p">.</span><span class="nx">walletBalances</span><span class="p">().</span><span class="nx">then</span><span class="p">((</span><span class="nx">data</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">error</span><span class="p">);</span>
<span class="p">});</span>
</code></pre></div><div class="highlight"><pre class="highlight go tab-go"><code><span class="k">package</span> <span class="n">main</span>

<span class="k">import</span> <span class="p">(</span>
    <span class="s">"fmt"</span>
    <span class="n">westwallet</span> <span class="s">"github.com/westwallet/westwallet-golang-api"</span>
<span class="p">)</span>

<span class="n">client</span> <span class="o">:=</span> <span class="n">westwallet</span><span class="o">.</span><span class="n">APIClient</span><span class="p">{</span>
    <span class="n">Key</span><span class="o">:</span>      <span class="s">"your_public_key"</span><span class="p">,</span>
    <span class="n">Secret</span><span class="o">:</span>   <span class="s">"your_private_key"</span><span class="p">,</span>
<span class="p">}</span>

<span class="n">balances</span><span class="p">,</span> <span class="n">err</span> <span class="o">:=</span> <span class="n">client</span><span class="o">.</span><span class="n">WalletBalances</span><span class="p">();</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="k">if</span> <span class="n">err</span> <span class="o">!=</span> <span class="no">nil</span> <span class="p">{</span>
    <span class="nb">panic</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">balance</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\CurrencyNotFoundException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>

<span class="nv">$balances</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">walletBalances</span><span class="p">();</span>
<span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$balances</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
</code></pre></div>
<p>All wallet&#39;s balances</p>
<h3 id='http-request-7'>HTTP request</h3>
<p><code>GET /wallet/balances</code></p>
<h1 id='addresses'>Addresses</h1><h2 id='generate-address'>Generate address</h2>
<blockquote>
<p><code>POST https://api.westwallet.io/address/generate</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rw2ciyaNshpHe7bCHo4bRWq6pqqynnWKQg"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">"755785168"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"XRP"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"label"</span><span class="p">:</span><span class="w"> </span><span class="s2">"your_label"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight python tab-python"><code><span class="kn">from</span> <span class="nn">westwallet_api</span> <span class="kn">import</span> <span class="n">WestWalletAPI</span>

<span class="n">client</span> <span class="o">=</span> <span class="n">WestWalletAPI</span><span class="p">(</span><span class="s">"your_public_key"</span><span class="p">,</span> <span class="s">"your_private_key"</span><span class="p">)</span>
<span class="n">address</span> <span class="o">=</span> <span class="n">client</span><span class="p">.</span><span class="n">generate_address</span><span class="p">(</span><span class="s">"XRP"</span><span class="p">,</span> <span class="s">"https://yourwebsite.com/ipn_url"</span><span class="p">,</span> <span class="s">"your_address_label"</span><span class="p">)</span>
<span class="k">print</span><span class="p">(</span><span class="n">address</span><span class="p">.</span><span class="n">address</span><span class="p">,</span> <span class="n">address</span><span class="p">.</span><span class="n">dest_tag</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight javascript tab-javascript"><code><span class="kd">const</span> <span class="nx">westwallet</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api</span><span class="dl">'</span><span class="p">);</span>
<span class="kd">const</span> <span class="nx">westwalletErrors</span> <span class="o">=</span> <span class="nx">require</span><span class="p">(</span><span class="dl">'</span><span class="s1">westwallet-api/lib/errors</span><span class="dl">'</span><span class="p">);</span>

<span class="kd">const</span> <span class="nx">publicKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPublicKey</span><span class="dl">"</span><span class="p">;</span>
<span class="kd">const</span> <span class="nx">privateKey</span> <span class="o">=</span> <span class="dl">"</span><span class="s2">yourPrivateKey</span><span class="dl">"</span><span class="p">;</span>

<span class="kd">let</span> <span class="nx">client</span> <span class="o">=</span> <span class="k">new</span> <span class="nx">westwallet</span><span class="p">.</span><span class="nx">WestWalletAPI</span><span class="p">(</span>
    <span class="nx">publicKey</span><span class="p">,</span> 
    <span class="nx">privateKey</span>
<span class="p">);</span>

<span class="nx">client</span><span class="p">.</span><span class="nx">generateAddress</span><span class="p">(</span><span class="dl">"</span><span class="s2">BTC</span><span class="dl">"</span><span class="p">,</span> <span class="dl">"</span><span class="s2">https://yourwebsite.com/ipn_url</span><span class="dl">"</span><span class="p">,</span> <span class="dl">"</span><span class="s2">your_address_label</span><span class="dl">"</span><span class="p">)</span>
<span class="p">.</span><span class="nx">then</span><span class="p">((</span><span class="nx">data</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>
<span class="p">}).</span><span class="k">catch</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">{</span>
    <span class="k">if</span> <span class="p">(</span><span class="nx">error</span> <span class="k">instanceof</span> <span class="nx">westwalletErrors</span><span class="p">.</span><span class="nx">CurrencyNotFoundError</span><span class="p">)</span> <span class="p">{</span>
        <span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="dl">"</span><span class="s2">No such currency</span><span class="dl">"</span><span class="p">);</span>
    <span class="p">}</span>
<span class="p">});</span>

</code></pre></div><div class="highlight"><pre class="highlight go tab-go"><code><span class="k">package</span> <span class="n">main</span>

<span class="k">import</span> <span class="p">(</span>
    <span class="s">"fmt"</span>
    <span class="n">westwallet</span> <span class="s">"github.com/westwallet/westwallet-golang-api"</span>
<span class="p">)</span>

<span class="n">client</span> <span class="o">:=</span> <span class="n">westwallet</span><span class="o">.</span><span class="n">APIClient</span><span class="p">{</span>
    <span class="n">Key</span><span class="o">:</span>      <span class="s">"your_public_key"</span><span class="p">,</span>
    <span class="n">Secret</span><span class="o">:</span>   <span class="s">"your_private_key"</span><span class="p">,</span>
<span class="p">}</span>

<span class="n">address</span><span class="p">,</span> <span class="n">err</span> <span class="o">:=</span> <span class="n">client</span><span class="o">.</span><span class="n">GenerateAddress</span><span class="p">(</span><span class="s">"BTC"</span><span class="p">,</span> <span class="s">"https://yourwebsite.com/ipn_url"</span><span class="p">,</span> <span class="s">"your_address_label"</span><span class="p">)</span>
<span class="k">if</span> <span class="n">err</span> <span class="o">!=</span> <span class="no">nil</span> <span class="p">{</span>
    <span class="nb">panic</span><span class="p">(</span><span class="n">err</span><span class="p">)</span>
<span class="p">}</span>
<span class="n">fmt</span><span class="o">.</span><span class="n">Println</span><span class="p">(</span><span class="n">address</span><span class="o">.</span><span class="n">Address</span><span class="p">)</span>
</code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\CurrencyNotFoundException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>

<span class="k">try</span> <span class="p">{</span>
    <span class="nv">$address</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">generateAddress</span><span class="p">(</span><span class="s2">"BTC"</span><span class="p">);</span>
    <span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$address</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span> <span class="k">catch</span><span class="p">(</span><span class="nc">CurrencyNotFoundException</span> <span class="nv">$e</span><span class="p">)</span> <span class="p">{</span>
    <span class="k">print</span><span class="p">(</span><span class="s2">"No such currency"</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div><h3 id='http-request-8'>HTTP request</h3>
<p><code>POST /address/generate</code></p>
<h3 id='post-params-5'>Post params</h3>
<table><thead>
<tr>
<th>Arguments</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currency</td>
<td>BTC</td>
<td>yes</td>
<td>currency of address that will be generate</td>
</tr>
<tr>
<td>ipn_url</td>
<td>https://yourwebsite.com/payment_secret_token?tx_id=123321</td>
<td>yes</td>
<td>Instant Payment Notification URL. At this address you will be notified as soon as someone will send funds for this address.</td>
</tr>
<tr>
<td>label</td>
<td>tx54543</td>
<td>no</td>
<td>Select a label for the addresses for further identification (maximum 30 characters)</td>
</tr>
</tbody></table>
<h1 id='payment-page-invoice'>Payment page (invoice)</h1>
<blockquote>
<p><code>POST https://api.westwallet.io/address/create_invoice</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"allowed_currencies_data"</span><span class="p">:</span><span class="w"> </span><span class="p">[{</span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.00211364</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"currency_code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">},</span><span class="w">
                              </span><span class="p">{</span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"rn97Zbg9V6hiqJoK6EQ8RtuaLUTYHSFXyw"</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.02658667</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"currency_code"</span><span class="p">:</span><span class="w"> </span><span class="s2">"XRP"</span><span class="p">,</span><span class="w">
                               </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">"10000324245"</span><span class="p">}],</span><span class="w">
  </span><span class="nl">"expire_at"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2022-01-31 19:36:14"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"token"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0b4ab755b42b6ff7fcb01100e42ec3"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"url"</span><span class="p">:</span><span class="w"> </span><span class="s2">"https://westwallet.io/payment/0b4ab755b42b6ff7fcb01100e42ec3"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><div class="highlight"><pre class="highlight php tab-php"><code><span class="cp">&lt;?php</span>
<span class="k">require_once</span> <span class="s1">'vendor/autoload.php'</span><span class="p">;</span>

<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\Client</span><span class="p">;</span>
<span class="kn">use</span> <span class="nc">WestWallet\WestWallet\InsufficientFundsException</span><span class="p">;</span>

<span class="nv">$client</span> <span class="o">=</span> <span class="k">new</span> <span class="nc">Client</span><span class="p">(</span><span class="s2">"your_public_key"</span><span class="p">,</span> <span class="s2">"your_private_key"</span><span class="p">);</span>
<span class="nv">$response</span> <span class="o">=</span> <span class="nv">$client</span><span class="o">-&gt;</span><span class="nf">createInvoice</span><span class="p">(</span><span class="k">array</span><span class="p">(</span><span class="s2">"BTC"</span><span class="p">,</span> <span class="s2">"USDTTRC"</span><span class="p">),</span> <span class="s2">"100"</span><span class="p">,</span> <span class="s2">"https://yourwebsite.com/payment_secret_token?tx_id=123321"</span><span class="p">,</span> <span class="kc">true</span><span class="p">,</span> <span class="s2">"https://yourwebsite.com/thank_you"</span><span class="p">,</span> <span class="s2">"Payment 1234"</span><span class="p">,</span> <span class="s2">"1234"</span><span class="p">,</span> <span class="mi">15</span><span class="p">);</span>
<span class="k">print</span><span class="p">(</span><span class="nb">implode</span><span class="p">(</span><span class="s2">"|"</span><span class="p">,</span> <span class="nv">$response</span><span class="p">)</span><span class="mf">.</span><span class="s2">"</span><span class="se">\n</span><span class="s2">"</span><span class="p">);</span>
</code></pre></div><h3 id='http-request-9'>HTTP request</h3>
<p><code>POST /address/create_invoice</code></p>
<h3 id='post-params-6'>Post params</h3>
<table><thead>
<tr>
<th>Arguments</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>currencies</td>
<td>[&quot;BTC&quot;, &quot;XRP&quot;]</td>
<td>yes</td>
<td>List of allowed cryptocurrencies for payment.</td>
</tr>
<tr>
<td>amount</td>
<td>100</td>
<td>yes</td>
<td>Requested amount of payment. If invoice has more than one currency in the currencies field, then the amount must be in USD.</td>
</tr>
<tr>
<td>amount_in_usd</td>
<td>true</td>
<td>no</td>
<td>Use this field if you need to specify an amount in USD when only one currency is specified in the currencies field. This field will automatically be true if more than one currency is specified in the currencies field.</td>
</tr>
<tr>
<td>ipn_url</td>
<td>https://yourwebsite.com/payment_secret_token?tx_id=123321</td>
<td>yes</td>
<td>Instant Payment Notification URL. At this address you will be notified as soon as someone will send funds for this address.</td>
</tr>
<tr>
<td>success_url</td>
<td>https://yourwebsite.com/thank_you</td>
<td>no</td>
<td>URL to redirect after successful payment</td>
</tr>
<tr>
<td>description</td>
<td>Payment 1234</td>
<td>no</td>
<td>Payment description. This is the text information that will be displayed on the payment page.</td>
</tr>
<tr>
<td>label</td>
<td>1234</td>
<td>no</td>
<td>Select a label for the addresses for further identification (maximum 30 characters)</td>
</tr>
<tr>
<td>ttl</td>
<td>15</td>
<td>no</td>
<td>Invoice lifetime in minutes. Maximum 90 minutes. After the expiration of this time, the payment interface (addresses, QR, etc.) will no longer be displayed on the invoice&#39;s page.</td>
</tr>
</tbody></table>
<h1 id='list-of-transactions-of-the-payment-page-invoice'>List of transactions of the payment page (invoice)</h1>
<p>View list of incoming transactions that were received to the addresses generated for the specified payment page.</p>

<blockquote>
<p><code>GET https://api.westwallet.io/address/invoice_transactions</code></p>
</blockquote>
<div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"count"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"error"</span><span class="p">:</span><span class="w"> </span><span class="s2">"ok"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"result"</span><span class="p">:</span><span class="w"> </span><span class="p">[{</span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"5mpjDRgoRYRmSnAXZTfB2bBkbpwvRjobXUjb4WYjF225"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="s2">"120"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"blockchain_confirmations"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
             </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"5RYQvquKnuEZyH3bumRqmnvwHsjTVgqFoBWc6jxyirDzsSjtgELRccnzoQJv3zdoPmR7xS657unUQhManbrttcQd"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"created_at"</span><span class="p">:</span><span class="w"> </span><span class="s2">"2023-07-31 13:04:12"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"SOL"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">,</span><span class="w">
             </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
             </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">763</span><span class="p">,</span><span class="w">
             </span><span class="nl">"label"</span><span class="p">:</span><span class="w"> </span><span class="s2">"434532"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"pending"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"type"</span><span class="p">:</span><span class="w"> </span><span class="s2">"receive"</span><span class="p">,</span><span class="w">
             </span><span class="nl">"updated_at"</span><span class="p">:</span><span class="w"> </span><span class="kc">null</span><span class="p">}]</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div><h3 id='http-request-10'>HTTP request</h3>
<p><code>GET /address/invoice_transactions?token=80f1321011b280877959f241a0e60b</code></p>
<h3 id='query-params-2'>Query params</h3>
<table><thead>
<tr>
<th>Argument</th>
<th>Example</th>
<th>Required</th>
<th>Description</th>
</tr>
</thead><tbody>
<tr>
<td>token</td>
<td>80f1321011b280877959f241a0e60b</td>
<td>yes</td>
<td>Payment page (invoice) token</td>
</tr>
</tbody></table>
<h1 id='notifications-about-payments'>Notifications about payments</h1><h2 id='receipt-transaction-notifications-incoming-transactions'>Receipt transaction notifications (incoming transactions)</h2><div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
  </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">123123</span><span class="p">,</span><span class="w">
  </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"**********************************"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
  </span><span class="nl">"label"</span><span class="p">:</span><span class="w"> </span><span class="s2">"312321"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"completed"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"blockchain_confirmations"</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w">
  </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0001"</span><span class="p">,</span><span class="w">
  </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"72648cefcc47b4371f28dc3328bc863918913eebf81b40d4a97d577b96c1ce53"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div>
<aside class="notice">
<strong>Attention!</strong> We are sending instant payment notifications (IPN) only from IP <code>***********</code> !
</aside>

<p>Once you have generated the address with <code>ipn_url</code> and got payment at this address,
you will get a POST-request on this url with <a href="/en/?json#notifications-about-payments">such a data structure</a>
Possible statuses in response body: <code>pending</code>, <code>completed</code>.</p>

<p>We are making request with header <code>Content-Type: application/x-www-form-urlencoded</code> .</p>

<aside class="notice">
<strong>Attention!</strong> To prevent request spoofing, check transaction status additionally with <code>POST /wallet/transaction</code>
</aside>
<h2 id='notifications-about-sending-transactions-outgoing-transactions'>Notifications about sending transactions (outgoing transactions)</h2><div class="highlight"><pre class="highlight json tab-json"><code><span class="p">{</span><span class="w">
    </span><span class="nl">"id"</span><span class="p">:</span><span class="w"> </span><span class="mi">321321</span><span class="p">,</span><span class="w">
    </span><span class="nl">"amount"</span><span class="p">:</span><span class="w"> </span><span class="mf">0.05</span><span class="p">,</span><span class="w">
    </span><span class="nl">"address"</span><span class="p">:</span><span class="w"> </span><span class="s2">"******************************************"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"dest_tag"</span><span class="p">:</span><span class="w"> </span><span class="s2">""</span><span class="p">,</span><span class="w">
    </span><span class="nl">"description"</span><span class="p">:</span><span class="w"> </span><span class="s2">"example"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"currency"</span><span class="p">:</span><span class="w"> </span><span class="s2">"BTC"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"status"</span><span class="p">:</span><span class="w"> </span><span class="s2">"completed"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"blockchain_confirmations"</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w">
    </span><span class="nl">"blockchain_hash"</span><span class="p">:</span><span class="w"> </span><span class="s2">"f23e3f9212b42ee78a536d3688ae7df7109965b321edb823e62ab706eecfc92a"</span><span class="p">,</span><span class="w">
    </span><span class="nl">"fee"</span><span class="p">:</span><span class="w"> </span><span class="s2">"0.0001"</span><span class="w">
</span><span class="p">}</span><span class="w">
</span></code></pre></div>
<aside class="notice">
<strong>Attention!</strong> We are sending instant payment notifications (IPN) only from IP <code>***********</code> !
</aside>

<p>After you have sent the funds, specify the `ipn_url&#39; in the request and the funds were actually sent, 
you will receive a POST request to this url with <a href="/uk/?json#notifications-about-sending-transactions-outgoing-transactions">the following data structure</a></p>

<p>We are making request with header <code>Content-Type: application/x-www-form-urlencoded</code> .</p>

<aside class="notice">
<strong>Attention!</strong> To prevent request spoofing, check transaction status additionally with <code>POST /wallet/transaction</code>
</aside>
<h1 id='possible-errors'>Possible errors</h1>
<p>Every response got field &quot;error&quot;. If it&#39;s &quot;ok&quot; - just pass. Else, check your error:</p>

<ul>
<li>&quot;wrong_auth&quot; - public key incorrect</li>
<li>&quot;ip_not_allowed&quot; - request IP is not in a list of allowed for this key</li>
<li>&quot;wrong_hmac&quot; - HMAC signature invalid</li>
<li>&quot;not_allowed_by_api_key&quot; - this type of actions is not allowed for this key</li>
<li>&quot;account_blocked&quot; - account has blocked</li>
<li>&quot;bad_address&quot; - address regexp validation failed</li>
<li>&quot;insufficient_funds&quot; - amount is greater than wallet balance</li>
<li>&quot;max_withdraw_error&quot; - amount is greater than maximal allowed, the maximum amount will be indicated in the max_withdraw field in the response body</li>
<li>&quot;min_withdraw_error&quot; - amount is less than minimal allowed</li>
<li>&quot;currency_not_found&quot; - wrong currency provided</li>
<li>&quot;currency_not_active&quot; - currency found but not active</li>
<li>&quot;deposit_not_allowed&quot; - deposit not allowed</li>
<li>&quot;not_found&quot; - transaction with such id wasn&#39;t found</li>
<li>&quot;wrong_ipn_url&quot; - IPN URL is invalid</li>
<li>&quot;self_address&quot; - you are trying to send to address that belongs to you</li>
<li>&quot;bad_priority&quot; - specified priority for transaction is not allowed</li>
<li>&quot;too_much_addresses_per_day&quot; - too many addresses generated in the last 24 hours</li>
</ul>
<h1 id='errors'>Errors</h1>
<p>WestWallet API uses the error codes for all requests:</p>

<table><thead>
<tr>
<th>Error Code</th>
<th>Meaning</th>
</tr>
</thead><tbody>
<tr>
<td>400</td>
<td>Bad Request - Incorrect request fields provided, explaination in the <code>error</code> field.</td>
</tr>
<tr>
<td>401</td>
<td>Unauthorized - API key is incorrect or not specified at all.</td>
</tr>
<tr>
<td>404</td>
<td>Not Found - Resource is not found. Check the documentation.</td>
</tr>
<tr>
<td>405</td>
<td>Method Not Allowed - You are trying to make a request using prohibited HTTP method.</td>
</tr>
<tr>
<td>429</td>
<td>Too Many Requests - You&#39;re making too much requests</td>
</tr>
<tr>
<td>500</td>
<td>Internal Server Error - The problem on the side of our server. Please notify our support.</td>
</tr>
</tbody></table>

<p>If request was successful you will receive a status <code>200</code>.</p>

      </div>
      <div class="dark-box">
          <div class="lang-selector">
                <a href="#" data-language-name="json">json</a>
                <a href="#" data-language-name="python">python</a>
                <a href="#" data-language-name="javascript">javascript</a>
                <a href="#" data-language-name="go">go</a>
                <a href="#" data-language-name="php">php</a>
          </div>
      </div>
    </div>
  </body>
</html>
