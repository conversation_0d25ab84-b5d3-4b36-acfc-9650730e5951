import admin from 'firebase-admin';
import serviceAccount from '$lib/server/phantom-app-e3c03-firebase-adminsdk-fbsvc-63c302ea2c.json';
import { promises as fs } from 'fs';

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount)
  });
}

// Load the most recent registration token from file
export async function getRegistrationToken() {
  const tokensFile = '../tokens.txt';
  try {
    const data = await fs.readFile(tokensFile, 'utf-8');
    const lines = data.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    if (lines.length === 0) {
      throw new Error('No registration tokens found');
    }
    console.log('[getRegistrationToken] Found token:', lines[lines.length - 1]);
    // Return the last (most recent) token
    return lines[lines.length - 1];
  } catch (err) {
    console.error('[getRegistrationToken] Failed to load token:', err);
    throw new Error('No registration token available');
  }
}

export async function sendNotification({ title, message }) {
  if (!title || !message) {
    throw new Error('Missing required fields');
  }
  let registrationToken;
  try {
    registrationToken = await getRegistrationToken();
  } catch (err) {
    throw new Error('No registration token for user');
  }
  const payload = {
    notification: {
      title,
      body: message
    },
    token: registrationToken
  };
  try {
    const response = await admin.messaging().send(payload);
    console.log(response);
    return { success: true, response };
  } catch (error) {
    console.error(error);
    throw new Error('Failed to send notification');
  }
}
