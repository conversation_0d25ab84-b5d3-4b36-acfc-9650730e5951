// Test script for WestWallet integration
// Run with: node src/lib/server/test-westwallet.js

import { westwalletService } from './westwallet-service.js';

async function testWestwalletIntegration() {
  console.log('🧪 Testing WestWallet Integration...\n');

  try {
    // Test 1: Get wallet balances
    console.log('📊 Testing wallet balances...');
    try {
      const balances = await westwalletService.getAllWalletBalances();
      console.log('✅ Wallet balances retrieved:', balances);
    } catch (error) {
      console.log('❌ Wallet balances failed:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: Create a test invoice
    console.log('💰 Testing invoice creation...');
    try {
      const testInvoice = await westwalletService.createInvoice(
        10, // $10 USD
        'https://example.com/ipn', // Test IPN URL
        'https://example.com/success', // Test success URL
        'Test deposit from integration test',
        'test-deposit-' + Date.now(),
        15 // 15 minutes TTL
      );
      console.log('✅ Test invoice created successfully:');
      console.log('   Invoice ID:', testInvoice.id);
      console.log('   Payment URL:', testInvoice.payment_url || testInvoice.url);
      console.log('   Available currencies:', Object.keys(testInvoice.addresses || {}));
      
      // Show first address as example
      if (testInvoice.addresses) {
        const firstCurrency = Object.keys(testInvoice.addresses)[0];
        if (firstCurrency) {
          const addressData = testInvoice.addresses[firstCurrency];
          console.log(`   Example (${firstCurrency}):`, addressData.address);
          console.log(`   Amount: ${addressData.amount} ${firstCurrency}`);
        }
      }
    } catch (error) {
      console.log('❌ Invoice creation failed:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Test individual currency balance
    console.log('🪙 Testing individual currency balance (BTC)...');
    try {
      const btcBalance = await westwalletService.getWalletBalance('BTC');
      console.log('✅ BTC balance retrieved:', btcBalance);
    } catch (error) {
      console.log('❌ BTC balance failed:', error.message);
    }

    console.log('\n✨ WestWallet integration test completed!');

  } catch (error) {
    console.error('💥 Test failed with error:', error);
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testWestwalletIntegration();
}

export { testWestwalletIntegration };
