import { WestWalletAPI, WestWalletAPIErrors } from './westwallet-js-api/index.js';
import { westwalletConfig, validateWestwalletConfig } from './westwallet-config.js';

class WestwalletService {
  constructor() {
    validateWestwalletConfig();
    this.client = new WestWalletAPI(
      westwalletConfig.apiKey,
      westwalletConfig.secretKey,
      westwalletConfig.baseUrl
    );
  }

  /**
   * Create a cryptocurrency invoice for deposit
   * @param {number} amountUSD - Amount in USD
   * @param {string} ipnUrl - IPN callback URL for payment notifications
   * @param {string} successUrl - Success redirect URL after payment
   * @param {string} description - Description for the invoice
   * @param {string} label - Label for the invoice
   * @param {number} ttlMinutes - Time to live in minutes (default: 60)
   * @returns {Promise<Object>} Invoice data from WestWallet
   */
  async createInvoice(amountUSD, ipnUrl, successUrl = undefined, description = undefined, label = undefined, ttlMinutes = 60) {
    try {
      // Create invoice with multiple cryptocurrencies
      const currencies = ['BTC', 'ETH', 'USDT', 'LTC']; // Popular cryptocurrencies
      
      const invoiceData = await this.client.createInvoice(
        currencies,
        amountUSD.toString(),
        true, // amountInUsd = true
        ipnUrl,
        successUrl,
        description,
        label,
        ttlMinutes
      );

      return invoiceData;
    } catch (error) {
      console.error('[WestwalletService] Error creating invoice:', error);
      
      // Handle specific WestWallet errors
      if (error instanceof WestWalletAPIErrors.WrongCredentialsError) {
        throw new Error('Invalid WestWallet API credentials');
      } else if (error instanceof WestWalletAPIErrors.NotAllowedError) {
        throw new Error('WestWallet API access not allowed');
      } else if (error instanceof WestWalletAPIErrors.InsufficientFundsError) {
        throw new Error('Insufficient funds in WestWallet account');
      }
      
      throw new Error(`Failed to create payment invoice: ${error.message}`);
    }
  }

  /**
   * Get transaction information by ID
   * @param {string|number} transactionId - Transaction ID
   * @returns {Promise<Object>} Transaction data
   */
  async getTransactionInfo(transactionId) {
    try {
      return await this.client.transactionInfo(transactionId);
    } catch (error) {
      console.error('[WestwalletService] Error getting transaction info:', error);
      
      if (error instanceof WestWalletAPIErrors.TransactionNotFoundError) {
        throw new Error('Transaction not found');
      }
      
      throw new Error(`Failed to get transaction info: ${error.message}`);
    }
  }

  /**
   * Get wallet balance for a specific currency
   * @param {string} currency - Currency code (e.g., 'BTC', 'ETH')
   * @returns {Promise<Object>} Balance data
   */
  async getWalletBalance(currency) {
    try {
      return await this.client.walletBalance(currency);
    } catch (error) {
      console.error('[WestwalletService] Error getting wallet balance:', error);
      
      if (error instanceof WestWalletAPIErrors.CurrencyNotFoundError) {
        throw new Error(`Currency ${currency} not found`);
      }
      
      throw new Error(`Failed to get wallet balance: ${error.message}`);
    }
  }

  /**
   * Get all wallet balances
   * @returns {Promise<Object>} All balances data
   */
  async getAllWalletBalances() {
    try {
      return await this.client.walletBalances();
    } catch (error) {
      console.error('[WestwalletService] Error getting all wallet balances:', error);
      throw new Error(`Failed to get wallet balances: ${error.message}`);
    }
  }

  /**
   * Generate a new address for a specific currency
   * @param {string} currency - Currency code
   * @param {string} ipnUrl - IPN callback URL
   * @param {string} label - Label for the address
   * @returns {Promise<Object>} Address data
   */
  async generateAddress(currency, ipnUrl = '', label = '') {
    try {
      return await this.client.generateAddress(currency, ipnUrl, label);
    } catch (error) {
      console.error('[WestwalletService] Error generating address:', error);
      
      if (error instanceof WestWalletAPIErrors.CurrencyNotFoundError) {
        throw new Error(`Currency ${currency} not found`);
      }
      
      throw new Error(`Failed to generate address: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const westwalletService = new WestwalletService();
export { WestWalletAPIErrors };
