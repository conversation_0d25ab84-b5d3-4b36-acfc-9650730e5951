import { env } from '$env/dynamic/private';

// WestWallet API configuration
export const westwalletConfig = {
  apiKey: env.WESTWALLET_API_KEY || '',
  secretKey: env.WESTWALLET_SECRET_KEY || '',
  baseUrl: env.WESTWALLET_BASE_URL || 'https://api.westwallet.io'
};

// Validate configuration
export function validateWestwalletConfig() {
  if (!westwalletConfig.apiKey) {
    throw new Error('WESTWALLET_API_KEY environment variable is required');
  }
  
  if (!westwalletConfig.secretKey) {
    throw new Error('WESTWALLET_SECRET_KEY environment variable is required');
  }
  
  return true;
}
